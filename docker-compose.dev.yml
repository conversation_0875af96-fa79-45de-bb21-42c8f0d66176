# AI法律助手开发环境数据库配置

version: '3.8'

services:
  # PostgreSQL主数据库
  postgres:
    image: postgres:15
    container_name: legal-assistant-postgres
    environment:
      POSTGRES_DB: legal_assistant
      POSTGRES_USER: legal_user
      POSTGRES_PASSWORD: legal_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployment/database/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./deployment/database/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_user -d legal_assistant"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - legal-network

  # MongoDB文档数据库
  mongodb:
    image: mongo:6.0
    container_name: legal-assistant-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: legal_user
      MONGO_INITDB_ROOT_PASSWORD: legal_password
      MONGO_INITDB_DATABASE: legal_db
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./deployment/database/mongodb/init.js:/docker-entrypoint-initdb.d/init.js
      - ./deployment/database/mongodb/mongod.conf:/etc/mongod.conf
    command: mongod --config /etc/mongod.conf
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/legal_db --quiet
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - legal-network

  # Redis缓存数据库
  redis:
    image: redis:7-alpine
    container_name: legal-assistant-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/database/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - legal-network

  # PostgreSQL管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: legal-assistant-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - legal-network

  # MongoDB管理工具
  mongo-express:
    image: mongo-express:latest
    container_name: legal-assistant-mongo-express
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: legal_user
      ME_CONFIG_MONGODB_ADMINPASSWORD: legal_password
      ME_CONFIG_MONGODB_URL: *************************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      - mongodb
    restart: unless-stopped
    networks:
      - legal-network

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: legal-assistant-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8082:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - legal-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: legal-assistant-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - legal-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: legal-assistant-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    networks:
      - legal-network

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  pgadmin_data:
    driver: local

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: legal-assistant-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./deployment/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./deployment/elasticsearch/plugins:/usr/share/elasticsearch/plugins
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - legal-network

  # Kibana可视化工具
  kibana:
    image: kibana:8.11.0
    container_name: legal-assistant-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      SERVER_NAME: kibana
      SERVER_HOST: "0.0.0.0"
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - legal-network

networks:
  legal-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
