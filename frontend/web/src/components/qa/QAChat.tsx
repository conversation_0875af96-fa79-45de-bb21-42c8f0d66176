/**
 * AI问答聊天组件
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  Avatar,
  Typography,
  Divider,
  Rate,
  message,
  Spin,
} from 'antd';
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  LikeOutlined,
  DislikeOutlined,
} from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';
import { qaAPI } from '@/services/api/qa';
import { QAResponse } from '@/services/api/qa';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  loading?: boolean;
  qaRecord?: QAResponse;
}

const QAChat: React.FC = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSend = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      loading: true,
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await qaAPI.askQuestion({
        question: userMessage.content,
      });

      // 更新助手消息
      setMessages(prev =>
        prev.map(msg =>
          msg.id === loadingMessage.id
            ? {
                ...msg,
                content: response.answer || '抱歉，我暂时无法回答这个问题。',
                loading: false,
                qaRecord: response,
              }
            : msg
        )
      );
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === loadingMessage.id
            ? {
                ...msg,
                content: '抱歉，服务暂时不可用，请稍后再试。',
                loading: false,
              }
            : msg
        )
      );
      message.error('发送失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 提交反馈
  const handleFeedback = async (messageId: string, score: number) => {
    const message = messages.find(msg => msg.id === messageId);
    if (!message?.qaRecord) return;

    try {
      await qaAPI.submitFeedback(message.qaRecord.id, {
        feedback_score: score,
      });
      message.success('反馈提交成功');
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('反馈提交失败');
    }
  };

  // 按Enter发送
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Card
      title="AI法律问答"
      style={{ height: '600px', display: 'flex', flexDirection: 'column' }}
      bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
    >
      {/* 消息列表 */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
          backgroundColor: '#fafafa',
        }}
      >
        {messages.length === 0 && (
          <div
            style={{
              textAlign: 'center',
              color: '#999',
              marginTop: '100px',
            }}
          >
            <RobotOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>您好！我是AI法律助手，有什么法律问题可以问我。</div>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              marginBottom: 16,
              display: 'flex',
              justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
            }}
          >
            <div
              style={{
                maxWidth: '70%',
                display: 'flex',
                flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                alignItems: 'flex-start',
                gap: 8,
              }}
            >
              <Avatar
                icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />}
                style={{
                  backgroundColor: message.type === 'user' ? '#1890ff' : '#52c41a',
                }}
              />
              <div
                style={{
                  backgroundColor: message.type === 'user' ? '#1890ff' : '#fff',
                  color: message.type === 'user' ? '#fff' : '#333',
                  padding: '12px 16px',
                  borderRadius: 8,
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                }}
              >
                {message.loading ? (
                  <Spin size="small" />
                ) : (
                  <>
                    <Paragraph
                      style={{
                        margin: 0,
                        color: message.type === 'user' ? '#fff' : '#333',
                        whiteSpace: 'pre-wrap',
                      }}
                    >
                      {message.content}
                    </Paragraph>
                    
                    {/* 助手消息的反馈按钮 */}
                    {message.type === 'assistant' && message.qaRecord && (
                      <div style={{ marginTop: 8, textAlign: 'right' }}>
                        <Space size="small">
                          <Button
                            type="text"
                            size="small"
                            icon={<LikeOutlined />}
                            onClick={() => handleFeedback(message.id, 5)}
                          />
                          <Button
                            type="text"
                            size="small"
                            icon={<DislikeOutlined />}
                            onClick={() => handleFeedback(message.id, 1)}
                          />
                        </Space>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <Divider style={{ margin: 0 }} />

      {/* 输入区域 */}
      <div style={{ padding: '16px' }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="请输入您的法律问题..."
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={loading}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            loading={loading}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </Space.Compact>
      </div>
    </Card>
  );
};

export default QAChat;
