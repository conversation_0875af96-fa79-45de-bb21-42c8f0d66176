/**
 * 系统状态组件
 */

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Alert, Spin } from 'antd';
import {
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';

interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_users: number;
  api_requests_per_minute: number;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  timestamp: string;
}

const SystemStatus: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchSystemData = async () => {
    try {
      setLoading(true);
      
      // 获取系统健康状态
      const healthResponse = await fetch('/api/v1/system/health');
      const healthData = await healthResponse.json();
      setHealth(healthData);

      // 获取系统指标（需要管理员权限）
      try {
        const metricsResponse = await fetch('/api/v1/system/metrics');
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setMetrics(metricsData);
        }
      } catch (error) {
        console.log('无法获取系统指标，可能没有管理员权限');
      }
    } catch (error) {
      console.error('获取系统数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemData();
    
    // 每30秒刷新一次
    const interval = setInterval(fetchSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'critical':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <CheckCircleOutlined />;
    }
  };

  const getProgressColor = (value: number) => {
    if (value < 50) return '#52c41a';
    if (value < 80) return '#faad14';
    return '#f5222d';
  };

  if (loading && !health) {
    return (
      <Card title="系统状态">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 系统健康状态 */}
      <Card 
        title="系统健康状态" 
        extra={
          <ReloadOutlined 
            onClick={fetchSystemData} 
            style={{ cursor: 'pointer' }}
          />
        }
        style={{ marginBottom: 16 }}
      >
        {health && (
          <Alert
            message={health.message}
            type={health.status === 'healthy' ? 'success' : 
                  health.status === 'warning' ? 'warning' : 'error'}
            icon={getStatusIcon(health.status)}
            showIcon
          />
        )}
      </Card>

      {/* 系统指标 */}
      {metrics && (
        <Card title="系统指标">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Card>
                <Statistic
                  title="CPU使用率"
                  value={metrics.cpu_usage}
                  suffix="%"
                  valueStyle={{ color: getProgressColor(metrics.cpu_usage) }}
                />
                <Progress
                  percent={metrics.cpu_usage}
                  strokeColor={getProgressColor(metrics.cpu_usage)}
                  showInfo={false}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card>
                <Statistic
                  title="内存使用率"
                  value={metrics.memory_usage}
                  suffix="%"
                  valueStyle={{ color: getProgressColor(metrics.memory_usage) }}
                />
                <Progress
                  percent={metrics.memory_usage}
                  strokeColor={getProgressColor(metrics.memory_usage)}
                  showInfo={false}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card>
                <Statistic
                  title="磁盘使用率"
                  value={metrics.disk_usage}
                  suffix="%"
                  valueStyle={{ color: getProgressColor(metrics.disk_usage) }}
                />
                <Progress
                  percent={metrics.disk_usage}
                  strokeColor={getProgressColor(metrics.disk_usage)}
                  showInfo={false}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card>
                <Statistic
                  title="在线用户"
                  value={metrics.active_users}
                  suffix="人"
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={8}>
              <Card>
                <Statistic
                  title="API请求"
                  value={metrics.api_requests_per_minute}
                  suffix="/分钟"
                />
              </Card>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default SystemStatus;
