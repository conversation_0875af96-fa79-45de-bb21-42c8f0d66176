/**
 * 空状态组件
 */

import React from 'react';
import { Empty, Button, Space } from 'antd';
import { EmptyProps } from 'antd/es/empty';

interface EmptyStateProps extends EmptyProps {
  title?: string;
  description?: string;
  action?: React.ReactNode;
  icon?: React.ReactNode;
  size?: 'small' | 'default' | 'large';
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  action,
  icon,
  size = 'default',
  ...props
}) => {
  const getImageSize = () => {
    switch (size) {
      case 'small':
        return { height: 60 };
      case 'large':
        return { height: 120 };
      default:
        return { height: 80 };
    }
  };

  return (
    <div
      style={{
        padding: size === 'small' ? '20px' : '40px 20px',
        textAlign: 'center',
      }}
    >
      <Empty
        image={icon || Empty.PRESENTED_IMAGE_SIMPLE}
        imageStyle={getImageSize()}
        description={
          <Space direction="vertical" size="small">
            {title && (
              <div style={{ fontSize: 16, fontWeight: 500, color: '#333' }}>
                {title}
              </div>
            )}
            {description && (
              <div style={{ fontSize: 14, color: '#999' }}>
                {description}
              </div>
            )}
          </Space>
        }
        {...props}
      >
        {action && <div style={{ marginTop: 16 }}>{action}</div>}
      </Empty>
    </div>
  );
};

export default EmptyState;
