/**
 * 响应式布局组件
 */

import React from 'react';
import { Row, Col, RowProps, ColProps } from 'antd';
import { useBreakpoint, useResponsiveGutter } from '@/hooks/useResponsive';

interface ResponsiveLayoutProps extends RowProps {
  children: React.ReactNode;
}

interface ResponsiveColProps extends ColProps {
  children: React.ReactNode;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  xxl?: number;
}

/**
 * 响应式行组件
 */
export const ResponsiveRow: React.FC<ResponsiveLayoutProps> = ({ 
  children, 
  gutter,
  ...props 
}) => {
  const responsiveGutter = useResponsiveGutter();
  
  return (
    <Row 
      gutter={gutter || responsiveGutter} 
      {...props}
    >
      {children}
    </Row>
  );
};

/**
 * 响应式列组件
 */
export const ResponsiveCol: React.FC<ResponsiveColProps> = ({ 
  children,
  xs = 24,
  sm = 12,
  md = 8,
  lg = 6,
  xl = 6,
  xxl = 4,
  ...props 
}) => {
  return (
    <Col 
      xs={xs}
      sm={sm}
      md={md}
      lg={lg}
      xl={xl}
      xxl={xxl}
      {...props}
    >
      {children}
    </Col>
  );
};

/**
 * 响应式网格组件
 */
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  gutter?: [number, number] | number;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
    xxl: 6,
  },
  gutter,
}) => {
  const breakpoint = useBreakpoint();
  const responsiveGutter = useResponsiveGutter();

  // 计算当前列数
  let currentColumns = columns.xs || 1;
  if (breakpoint.sm) currentColumns = columns.sm || currentColumns;
  if (breakpoint.md) currentColumns = columns.md || currentColumns;
  if (breakpoint.lg) currentColumns = columns.lg || currentColumns;
  if (breakpoint.xl) currentColumns = columns.xl || currentColumns;
  if (breakpoint.xxl) currentColumns = columns.xxl || currentColumns;

  // 计算每列的span
  const colSpan = 24 / currentColumns;

  return (
    <Row gutter={gutter || responsiveGutter}>
      {React.Children.map(children, (child, index) => (
        <Col key={index} span={colSpan}>
          {child}
        </Col>
      ))}
    </Row>
  );
};

/**
 * 移动端适配容器
 */
interface MobileAdaptiveProps {
  children: React.ReactNode;
  mobileComponent?: React.ReactNode;
  breakpoint?: number;
}

export const MobileAdaptive: React.FC<MobileAdaptiveProps> = ({
  children,
  mobileComponent,
  breakpoint = 768,
}) => {
  const screenBreakpoint = useBreakpoint();
  const isMobile = !screenBreakpoint.md;

  if (isMobile && mobileComponent) {
    return <>{mobileComponent}</>;
  }

  return <>{children}</>;
};

/**
 * 响应式卡片网格
 */
interface ResponsiveCardGridProps {
  children: React.ReactNode;
  minCardWidth?: number;
  maxColumns?: number;
  gutter?: [number, number] | number;
}

export const ResponsiveCardGrid: React.FC<ResponsiveCardGridProps> = ({
  children,
  minCardWidth = 300,
  maxColumns = 4,
  gutter,
}) => {
  const breakpoint = useBreakpoint();
  const responsiveGutter = useResponsiveGutter();

  // 根据屏幕宽度和最小卡片宽度计算列数
  const calculateColumns = () => {
    if (!breakpoint.sm) return 1;
    if (!breakpoint.md) return 2;
    if (!breakpoint.lg) return Math.min(3, maxColumns);
    return Math.min(4, maxColumns);
  };

  const columns = calculateColumns();
  const colSpan = 24 / columns;

  return (
    <Row gutter={gutter || responsiveGutter}>
      {React.Children.map(children, (child, index) => (
        <Col key={index} span={colSpan}>
          {child}
        </Col>
      ))}
    </Row>
  );
};
