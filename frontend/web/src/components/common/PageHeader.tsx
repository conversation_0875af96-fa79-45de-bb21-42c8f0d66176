/**
 * 页面头部组件
 */

import React from 'react';
import { PageHeader as AntPageHeader, Breadcrumb, Space, Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useRouter } from 'next/router';

interface BreadcrumbItem {
  title: string;
  href?: string;
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  showBack?: boolean;
  extra?: React.ReactNode;
  children?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs,
  showBack = false,
  extra,
  children,
}) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <div style={{ marginBottom: 24 }}>
      {/* 面包屑导航 */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumb style={{ marginBottom: 16 }}>
          {breadcrumbs.map((item, index) => (
            <Breadcrumb.Item key={index}>
              {item.href ? (
                <a onClick={() => router.push(item.href!)}>{item.title}</a>
              ) : (
                item.title
              )}
            </Breadcrumb.Item>
          ))}
        </Breadcrumb>
      )}

      {/* 页面头部 */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: children ? 16 : 0,
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {showBack && (
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 16 }}
            />
          )}
          <div>
            <h1 style={{ margin: 0, fontSize: 24, fontWeight: 600 }}>
              {title}
            </h1>
            {subtitle && (
              <p style={{ margin: '4px 0 0 0', color: '#666', fontSize: 14 }}>
                {subtitle}
              </p>
            )}
          </div>
        </div>

        {extra && <Space>{extra}</Space>}
      </div>

      {/* 额外内容 */}
      {children && <div>{children}</div>}
    </div>
  );
};

export default PageHeader;
