/**
 * 全局状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types/user';

// 应用状态接口
interface AppState {
  // 用户状态
  user: User | null;
  isAuthenticated: boolean;
  
  // UI状态
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  
  // 加载状态
  loading: {
    global: boolean;
    auth: boolean;
    qa: boolean;
    cases: boolean;
    contracts: boolean;
    documents: boolean;
  };
  
  // 错误状态
  error: {
    global: string | null;
    auth: string | null;
    qa: string | null;
    cases: string | null;
    contracts: string | null;
    documents: string | null;
  };
  
  // 缓存数据
  cache: {
    qaHistory: any[];
    contractHistory: any[];
    documentHistory: any[];
    favorites: any[];
  };
}

// 应用操作接口
interface AppActions {
  // 用户操作
  setUser: (user: User | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  
  // UI操作
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  
  // 加载状态操作
  setLoading: (key: keyof AppState['loading'], loading: boolean) => void;
  setGlobalLoading: (loading: boolean) => void;
  
  // 错误状态操作
  setError: (key: keyof AppState['error'], error: string | null) => void;
  clearError: (key: keyof AppState['error']) => void;
  clearAllErrors: () => void;
  
  // 缓存操作
  setCache: (key: keyof AppState['cache'], data: any[]) => void;
  clearCache: (key?: keyof AppState['cache']) => void;
  
  // 重置状态
  reset: () => void;
}

// 初始状态
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  sidebarCollapsed: false,
  theme: 'light',
  loading: {
    global: false,
    auth: false,
    qa: false,
    cases: false,
    contracts: false,
    documents: false,
  },
  error: {
    global: null,
    auth: null,
    qa: null,
    cases: null,
    contracts: null,
    documents: null,
  },
  cache: {
    qaHistory: [],
    contractHistory: [],
    documentHistory: [],
    favorites: [],
  },
};

// 创建store
export const useAppStore = create<AppState & AppActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // 用户操作
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
      
      // UI操作
      toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setTheme: (theme) => set({ theme }),
      
      // 加载状态操作
      setLoading: (key, loading) =>
        set((state) => ({
          loading: { ...state.loading, [key]: loading },
        })),
      setGlobalLoading: (loading) =>
        set((state) => ({
          loading: { ...state.loading, global: loading },
        })),
      
      // 错误状态操作
      setError: (key, error) =>
        set((state) => ({
          error: { ...state.error, [key]: error },
        })),
      clearError: (key) =>
        set((state) => ({
          error: { ...state.error, [key]: null },
        })),
      clearAllErrors: () =>
        set({
          error: {
            global: null,
            auth: null,
            qa: null,
            cases: null,
            contracts: null,
            documents: null,
          },
        }),
      
      // 缓存操作
      setCache: (key, data) =>
        set((state) => ({
          cache: { ...state.cache, [key]: data },
        })),
      clearCache: (key) => {
        if (key) {
          set((state) => ({
            cache: { ...state.cache, [key]: [] },
          }));
        } else {
          set({
            cache: {
              qaHistory: [],
              contractHistory: [],
              documentHistory: [],
              favorites: [],
            },
          });
        }
      },
      
      // 重置状态
      reset: () => set(initialState),
    }),
    {
      name: 'app-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        sidebarCollapsed: state.sidebarCollapsed,
        theme: state.theme,
        cache: state.cache,
      }),
    }
  )
);

// 选择器
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useSidebarCollapsed = () => useAppStore((state) => state.sidebarCollapsed);
export const useTheme = () => useAppStore((state) => state.theme);
export const useLoading = () => useAppStore((state) => state.loading);
export const useError = () => useAppStore((state) => state.error);
export const useCache = () => useAppStore((state) => state.cache);

// 操作选择器
export const useAppActions = () => useAppStore((state) => ({
  setUser: state.setUser,
  setAuthenticated: state.setAuthenticated,
  toggleSidebar: state.toggleSidebar,
  setSidebarCollapsed: state.setSidebarCollapsed,
  setTheme: state.setTheme,
  setLoading: state.setLoading,
  setGlobalLoading: state.setGlobalLoading,
  setError: state.setError,
  clearError: state.clearError,
  clearAllErrors: state.clearAllErrors,
  setCache: state.setCache,
  clearCache: state.clearCache,
  reset: state.reset,
}));
