/**
 * 本地存储Hook
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * 本地存储Hook
 */
export const useLocalStorage = <T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] => {
  // 获取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // 设置值
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);

        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // 删除值
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
};

/**
 * 会话存储Hook
 */
export const useSessionStorage = <T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] => {
  // 获取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // 设置值
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);

        if (typeof window !== 'undefined') {
          window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.error(`Error setting sessionStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // 删除值
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      if (typeof window !== 'undefined') {
        window.sessionStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
};

/**
 * 存储事件监听Hook
 */
export const useStorageListener = (
  key: string,
  callback: (newValue: string | null, oldValue: string | null) => void
) => {
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key) {
        callback(e.newValue, e.oldValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, callback]);
};

/**
 * 缓存Hook（带过期时间）
 */
interface CacheItem<T> {
  value: T;
  timestamp: number;
  ttl: number;
}

export const useCache = <T>(
  key: string,
  initialValue: T,
  ttl: number = 5 * 60 * 1000 // 默认5分钟
): [T, (value: T) => void, () => void, boolean] => {
  const [cachedValue, setCachedValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(`cache_${key}`);
      if (item) {
        const cacheItem: CacheItem<T> = JSON.parse(item);
        const now = Date.now();
        
        // 检查是否过期
        if (now - cacheItem.timestamp < cacheItem.ttl) {
          return cacheItem.value;
        } else {
          // 过期了，删除缓存
          window.localStorage.removeItem(`cache_${key}`);
        }
      }
    } catch (error) {
      console.error(`Error reading cache key "${key}":`, error);
    }

    return initialValue;
  });

  // 检查是否有效
  const [isValid, setIsValid] = useState<boolean>(() => {
    if (typeof window === 'undefined') {
      return false;
    }

    try {
      const item = window.localStorage.getItem(`cache_${key}`);
      if (item) {
        const cacheItem: CacheItem<T> = JSON.parse(item);
        const now = Date.now();
        return now - cacheItem.timestamp < cacheItem.ttl;
      }
    } catch (error) {
      console.error(`Error checking cache validity for key "${key}":`, error);
    }

    return false;
  });

  // 设置缓存
  const setCacheValue = useCallback(
    (value: T) => {
      try {
        const cacheItem: CacheItem<T> = {
          value,
          timestamp: Date.now(),
          ttl,
        };

        setCachedValue(value);
        setIsValid(true);

        if (typeof window !== 'undefined') {
          window.localStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
        }
      } catch (error) {
        console.error(`Error setting cache key "${key}":`, error);
      }
    },
    [key, ttl]
  );

  // 清除缓存
  const clearCache = useCallback(() => {
    try {
      setCachedValue(initialValue);
      setIsValid(false);

      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(`cache_${key}`);
      }
    } catch (error) {
      console.error(`Error clearing cache key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [cachedValue, setCacheValue, clearCache, isValid];
};
