/**
 * 响应式Hook
 */

import { useState, useEffect } from 'react';

interface BreakpointMap {
  xs: boolean;
  sm: boolean;
  md: boolean;
  lg: boolean;
  xl: boolean;
  xxl: boolean;
}

interface ScreenSize {
  width: number;
  height: number;
}

const breakpoints = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
};

/**
 * 响应式断点Hook
 */
export const useBreakpoint = (): BreakpointMap => {
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    xs: screenSize.width >= breakpoints.xs,
    sm: screenSize.width >= breakpoints.sm,
    md: screenSize.width >= breakpoints.md,
    lg: screenSize.width >= breakpoints.lg,
    xl: screenSize.width >= breakpoints.xl,
    xxl: screenSize.width >= breakpoints.xxl,
  };
};

/**
 * 移动端检测Hook
 */
export const useIsMobile = (): boolean => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      const userAgent = navigator.userAgent;
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
      const isMobileDevice = mobileRegex.test(userAgent);
      const isSmallScreen = window.innerWidth < breakpoints.md;
      
      setIsMobile(isMobileDevice || isSmallScreen);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
};

/**
 * 屏幕尺寸Hook
 */
export const useScreenSize = (): ScreenSize => {
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return screenSize;
};

/**
 * 响应式列数Hook
 */
export const useResponsiveColumns = (
  defaultColumns: number = 4,
  breakpointColumns?: Partial<Record<keyof typeof breakpoints, number>>
): number => {
  const breakpoint = useBreakpoint();
  const [columns, setColumns] = useState(defaultColumns);

  useEffect(() => {
    const currentBreakpoints = Object.entries(breakpoints)
      .reverse()
      .find(([key]) => breakpoint[key as keyof BreakpointMap]);

    if (currentBreakpoints && breakpointColumns) {
      const [breakpointKey] = currentBreakpoints;
      const breakpointColumns_ = breakpointColumns[breakpointKey as keyof typeof breakpoints];
      
      if (breakpointColumns_) {
        setColumns(breakpointColumns_);
        return;
      }
    }

    // 默认响应式列数
    if (!breakpoint.md) {
      setColumns(1);
    } else if (!breakpoint.lg) {
      setColumns(2);
    } else if (!breakpoint.xl) {
      setColumns(3);
    } else {
      setColumns(defaultColumns);
    }
  }, [breakpoint, defaultColumns, breakpointColumns]);

  return columns;
};

/**
 * 响应式间距Hook
 */
export const useResponsiveGutter = (): [number, number] => {
  const breakpoint = useBreakpoint();

  if (!breakpoint.md) {
    return [8, 8];
  } else if (!breakpoint.lg) {
    return [12, 12];
  } else {
    return [16, 16];
  }
};
