/**
 * 自定义查询Hook
 */

import { useQuery as useReactQuery, useMutation, useQueryClient } from 'react-query';
import { message } from 'antd';

// 查询配置
export interface QueryConfig {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
  retry?: number | boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

// 变更配置
export interface MutationConfig {
  onSuccess?: (data: any, variables: any) => void;
  onError?: (error: any, variables: any) => void;
  onSettled?: (data: any, error: any, variables: any) => void;
}

/**
 * 自定义查询Hook
 */
export const useQuery = <T = any>(
  key: string | string[],
  queryFn: () => Promise<T>,
  config: QueryConfig = {}
) => {
  return useReactQuery(key, queryFn, {
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
    refetchOnWindowFocus: false,
    retry: 1,
    ...config,
    onError: (error: any) => {
      if (config.onError) {
        config.onError(error);
      } else {
        console.error('Query error:', error);
      }
    },
  });
};

/**
 * 自定义变更Hook
 */
export const useMutation = <T = any, V = any>(
  mutationFn: (variables: V) => Promise<T>,
  config: MutationConfig = {}
) => {
  const queryClient = useQueryClient();

  return useMutation(mutationFn, {
    ...config,
    onSuccess: (data, variables) => {
      if (config.onSuccess) {
        config.onSuccess(data, variables);
      }
    },
    onError: (error: any, variables) => {
      if (config.onError) {
        config.onError(error, variables);
      } else {
        const errorMessage = error.response?.data?.message || error.message || '操作失败';
        message.error(errorMessage);
      }
    },
    onSettled: (data, error, variables) => {
      if (config.onSettled) {
        config.onSettled(data, error, variables);
      }
    },
  });
};

/**
 * 分页查询Hook
 */
export interface PaginationParams {
  page: number;
  size: number;
}

export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export const usePaginatedQuery = <T = any>(
  key: string | string[],
  queryFn: (params: PaginationParams) => Promise<PaginatedData<T>>,
  initialParams: PaginationParams = { page: 1, size: 20 },
  config: QueryConfig = {}
) => {
  const [params, setParams] = useState(initialParams);

  const query = useQuery(
    [...(Array.isArray(key) ? key : [key]), params],
    () => queryFn(params),
    config
  );

  const changePage = (page: number, size?: number) => {
    setParams(prev => ({
      page,
      size: size || prev.size,
    }));
  };

  const changePageSize = (size: number) => {
    setParams(prev => ({
      page: 1,
      size,
    }));
  };

  return {
    ...query,
    params,
    changePage,
    changePageSize,
    pagination: {
      current: params.page,
      pageSize: params.size,
      total: query.data?.total || 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      onChange: changePage,
      onShowSizeChange: (current: number, size: number) => {
        changePageSize(size);
      },
    },
  };
};

/**
 * 无限滚动查询Hook
 */
export const useInfiniteQuery = <T = any>(
  key: string | string[],
  queryFn: (params: { page: number; size: number }) => Promise<PaginatedData<T>>,
  config: QueryConfig = {}
) => {
  const queryClient = useQueryClient();
  const [allData, setAllData] = useState<T[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const loadMore = async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const response = await queryFn({ page, size: 20 });
      
      if (page === 1) {
        setAllData(response.items);
      } else {
        setAllData(prev => [...prev, ...response.items]);
      }

      setHasMore(response.items.length === 20 && page < response.pages);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Load more error:', error);
    } finally {
      setLoading(false);
    }
  };

  const refresh = () => {
    setPage(1);
    setHasMore(true);
    setAllData([]);
    loadMore();
  };

  useEffect(() => {
    loadMore();
  }, []);

  return {
    data: allData,
    loading,
    hasMore,
    loadMore,
    refresh,
  };
};

// 导入useState和useEffect
import { useState, useEffect } from 'react';
