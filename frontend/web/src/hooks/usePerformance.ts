/**
 * 性能监控Hook
 */

import { useEffect, useCallback, useRef } from 'react';

// 性能指标接口
interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

// 性能监控Hook
export const usePerformance = () => {
  const metricsRef = useRef<Partial<PerformanceMetrics>>({});

  // 获取FCP
  const getFCP = useCallback(() => {
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcpEntry ? fcpEntry.startTime : 0;
  }, []);

  // 获取LCP
  const getLCP = useCallback(() => {
    return new Promise<number>((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
          observer.disconnect();
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } else {
        resolve(0);
      }
    });
  }, []);

  // 获取FID
  const getFID = useCallback(() => {
    return new Promise<number>((resolve) => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const firstEntry = entries[0];
          resolve(firstEntry.processingStart - firstEntry.startTime);
          observer.disconnect();
        });
        observer.observe({ entryTypes: ['first-input'] });
      } else {
        resolve(0);
      }
    });
  }, []);

  // 获取CLS
  const getCLS = useCallback(() => {
    return new Promise<number>((resolve) => {
      let clsValue = 0;
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
        });
        observer.observe({ entryTypes: ['layout-shift'] });
        
        // 5秒后返回结果
        setTimeout(() => {
          resolve(clsValue);
          observer.disconnect();
        }, 5000);
      } else {
        resolve(0);
      }
    });
  }, []);

  // 获取TTFB
  const getTTFB = useCallback(() => {
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navigationEntry ? navigationEntry.responseStart - navigationEntry.requestStart : 0;
  }, []);

  // 收集所有性能指标
  const collectMetrics = useCallback(async () => {
    const metrics: Partial<PerformanceMetrics> = {};
    
    metrics.fcp = getFCP();
    metrics.ttfb = getTTFB();
    
    try {
      metrics.lcp = await getLCP();
      metrics.fid = await getFID();
      metrics.cls = await getCLS();
    } catch (error) {
      console.error('收集性能指标失败:', error);
    }
    
    metricsRef.current = metrics;
    return metrics;
  }, [getFCP, getTTFB, getLCP, getFID, getCLS]);

  // 发送性能数据
  const sendMetrics = useCallback((metrics: Partial<PerformanceMetrics>) => {
    // 这里可以发送到分析服务
    console.log('性能指标:', metrics);
    
    // 示例：发送到Google Analytics
    if (typeof gtag !== 'undefined') {
      Object.entries(metrics).forEach(([key, value]) => {
        if (value !== undefined) {
          gtag('event', 'performance_metric', {
            metric_name: key,
            metric_value: Math.round(value),
          });
        }
      });
    }
  }, []);

  useEffect(() => {
    // 页面加载完成后收集指标
    const handleLoad = async () => {
      // 等待一段时间确保所有资源加载完成
      setTimeout(async () => {
        const metrics = await collectMetrics();
        sendMetrics(metrics);
      }, 1000);
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, [collectMetrics, sendMetrics]);

  return {
    metrics: metricsRef.current,
    collectMetrics,
    sendMetrics,
  };
};

// 资源加载监控Hook
export const useResourceMonitor = () => {
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const resource = entry as PerformanceResourceTiming;
        
        // 监控慢资源
        if (resource.duration > 1000) {
          console.warn('慢资源加载:', {
            name: resource.name,
            duration: resource.duration,
            size: resource.transferSize,
          });
        }
        
        // 监控失败的资源
        if (resource.transferSize === 0 && resource.duration > 0) {
          console.error('资源加载失败:', resource.name);
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
    
    return () => observer.disconnect();
  }, []);
};

// 内存监控Hook
export const useMemoryMonitor = () => {
  const checkMemory = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      };
    }
    return null;
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      const memoryInfo = checkMemory();
      if (memoryInfo && memoryInfo.usage > 80) {
        console.warn('内存使用率过高:', memoryInfo);
      }
    }, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, [checkMemory]);

  return { checkMemory };
};

// 网络状态监控Hook
export const useNetworkMonitor = () => {
  const [networkInfo, setNetworkInfo] = useState<any>(null);

  useEffect(() => {
    const updateNetworkInfo = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
        });
      }
    };

    updateNetworkInfo();

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', updateNetworkInfo);
      
      return () => {
        connection.removeEventListener('change', updateNetworkInfo);
      };
    }
  }, []);

  return networkInfo;
};

// 导入useState
import { useState } from 'react';
