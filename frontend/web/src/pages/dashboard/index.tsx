/**
 * 仪表板页面
 */

import React from 'react';
import { Row, Col, Card, Statistic, Button, List, Typography, Space } from 'antd';
import {
  QuestionCircleOutlined,
  FileTextOutlined,
  ContainerOutlined,
  SolutionOutlined,
  StarOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/router';

import { useAuth } from '@/contexts/AuthContext';
import PageHeader from '@/components/common/PageHeader';

const { Title, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();

  // 快捷功能
  const quickActions = [
    {
      title: 'AI法律问答',
      description: '智能回答您的法律问题',
      icon: <QuestionCircleOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      path: '/qa',
    },
    {
      title: '案例检索',
      description: '搜索相关法律案例',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      path: '/cases',
    },
    {
      title: '合同分析',
      description: '智能分析合同风险',
      icon: <ContainerOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      path: '/contracts',
    },
    {
      title: '文书生成',
      description: '自动生成法律文书',
      icon: <SolutionOutlined style={{ fontSize: 24, color: '#f5222d' }} />,
      path: '/documents',
    },
  ];

  // 最近活动（模拟数据）
  const recentActivities = [
    {
      title: '提问了关于劳动合同的问题',
      time: '2小时前',
      type: 'qa',
    },
    {
      title: '分析了房屋租赁合同',
      time: '1天前',
      type: 'contract',
    },
    {
      title: '生成了民事起诉状',
      time: '2天前',
      type: 'document',
    },
    {
      title: '搜索了交通事故案例',
      time: '3天前',
      type: 'case',
    },
  ];

  return (
    <div>
      <PageHeader
        title={`欢迎回来，${user?.full_name || user?.username || '用户'}`}
        subtitle="AI法律助手为您提供专业的法律服务"
      />

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总提问数"
              value={23}
              prefix={<QuestionCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="合同分析"
              value={8}
              prefix={<ContainerOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="文书生成"
              value={5}
              prefix={<SolutionOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="收藏数量"
              value={12}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 快捷功能 */}
        <Col xs={24} lg={16}>
          <Card title="快捷功能" style={{ height: '100%' }}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card
                    hoverable
                    onClick={() => router.push(action.path)}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <Space direction="vertical" size="middle">
                      {action.icon}
                      <div>
                        <Title level={5} style={{ margin: 0 }}>
                          {action.title}
                        </Title>
                        <Paragraph type="secondary" style={{ margin: 0 }}>
                          {action.description}
                        </Paragraph>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={8}>
          <Card
            title="最近活动"
            extra={
              <Button
                type="link"
                icon={<HistoryOutlined />}
                onClick={() => router.push('/history')}
              >
                查看全部
              </Button>
            }
            style={{ height: '100%' }}
          >
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={item.title}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
