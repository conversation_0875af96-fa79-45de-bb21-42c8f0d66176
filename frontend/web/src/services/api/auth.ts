/**
 * 认证相关API
 */

import { api } from './client';
import { User } from '@/types/user';

// 登录请求参数
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

// 注册请求参数
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  phone?: string;
  user_type?: 'individual' | 'enterprise' | 'lawyer';
}

// 刷新token请求参数
export interface RefreshTokenRequest {
  refresh_token: string;
}

// 密码重置请求参数
export interface PasswordResetRequest {
  email: string;
}

// 密码重置确认参数
export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

// 认证API
export const authAPI = {
  // 用户登录
  login: (data: LoginRequest): Promise<LoginResponse> =>
    api.post('/auth/login/json', data),

  // 用户注册
  register: (data: RegisterRequest): Promise<User> =>
    api.post('/auth/register', data),

  // 刷新token
  refreshToken: (refreshToken: string): Promise<LoginResponse> =>
    api.post('/auth/refresh', { refresh_token: refreshToken }),

  // 用户登出
  logout: (): Promise<void> =>
    api.post('/auth/logout'),

  // 获取当前用户信息
  getCurrentUser: (): Promise<User> =>
    api.get('/auth/me'),

  // 发送邮箱验证
  sendEmailVerification: (email: string): Promise<void> =>
    api.post('/auth/verify-email', { email }),

  // 确认邮箱验证
  confirmEmailVerification: (token: string): Promise<void> =>
    api.post('/auth/confirm-email', { token }),

  // 发送密码重置邮件
  sendPasswordReset: (data: PasswordResetRequest): Promise<void> =>
    api.post('/auth/password-reset', data),

  // 确认密码重置
  confirmPasswordReset: (data: PasswordResetConfirm): Promise<void> =>
    api.post('/auth/password-reset-confirm', data),
};
