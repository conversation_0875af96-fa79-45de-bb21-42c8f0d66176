/**
 * API客户端配置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import Cookies from 'js-cookie';

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = Cookies.get('access_token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    const requestId = Math.random().toString(36).substring(7);
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId;
    }

    // 日志记录
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      data: config.data,
      params: config.params,
    });

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 日志记录
    console.log(`[API Response] ${response.status} ${response.config.url}`, {
      requestId: response.config.headers?.['X-Request-ID'],
      data: response.data,
    });

    return response;
  },
  async (error) => {
    const { response, config } = error;

    // 日志记录
    console.error(`[API Response Error] ${response?.status} ${config?.url}`, {
      requestId: config?.headers?.['X-Request-ID'],
      error: response?.data,
    });

    // 处理不同的错误状态
    if (response) {
      switch (response.status) {
        case 401:
          // 未授权，尝试刷新token
          const refreshToken = Cookies.get('refresh_token');
          if (refreshToken && !config._retry) {
            config._retry = true;
            try {
              const refreshResponse = await axios.post(
                `${API_BASE_URL}/api/v1/auth/refresh`,
                { refresh_token: refreshToken }
              );
              
              const { access_token } = refreshResponse.data;
              Cookies.set('access_token', access_token, { expires: 7 });
              
              // 重新发送原请求
              config.headers.Authorization = `Bearer ${access_token}`;
              return apiClient(config);
            } catch (refreshError) {
              // 刷新失败，跳转到登录页
              Cookies.remove('access_token');
              Cookies.remove('refresh_token');
              window.location.href = '/auth/login';
              return Promise.reject(refreshError);
            }
          } else {
            // 没有refresh token或已经重试过，跳转到登录页
            Cookies.remove('access_token');
            Cookies.remove('refresh_token');
            window.location.href = '/auth/login';
          }
          break;

        case 403:
          message.error('权限不足');
          break;

        case 404:
          message.error('请求的资源不存在');
          break;

        case 429:
          message.error('请求过于频繁，请稍后再试');
          break;

        case 500:
          message.error('服务器内部错误');
          break;

        case 502:
        case 503:
        case 504:
          message.error('服务暂时不可用，请稍后再试');
          break;

        default:
          // 显示服务器返回的错误信息
          const errorMessage = response.data?.message || response.data?.detail || '请求失败';
          message.error(errorMessage);
      }
    } else if (error.code === 'ECONNABORTED') {
      message.error('请求超时，请检查网络连接');
    } else if (error.message === 'Network Error') {
      message.error('网络连接失败，请检查网络');
    } else {
      message.error('未知错误，请稍后再试');
    }

    return Promise.reject(error);
  }
);

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 通用API方法
export const api = {
  // GET请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.get(url, config).then(response => response.data),

  // POST请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, data, config).then(response => response.data),

  // PUT请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.put(url, data, config).then(response => response.data),

  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.patch(url, data, config).then(response => response.data),

  // DELETE请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.delete(url, config).then(response => response.data),

  // 文件上传
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }).then(response => response.data),

  // 文件下载
  download: (url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> =>
    apiClient.get(url, {
      ...config,
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    }),
};

export default apiClient;
