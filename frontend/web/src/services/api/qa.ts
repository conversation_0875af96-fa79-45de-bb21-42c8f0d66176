/**
 * 问答相关API
 */

import { api, PaginatedResponse } from './client';

// 问答请求参数
export interface QARequest {
  question: string;
  category?: string;
  session_id?: string;
}

// 问答响应
export interface QAResponse {
  id: string;
  question: string;
  answer?: string;
  category?: string;
  confidence_score?: number;
  feedback_score?: number;
  status: 'pending' | 'completed' | 'failed';
  metadata?: Record<string, any>;
  created_at: string;
  updated_at?: string;
}

// 问答历史响应
export interface QAHistoryResponse {
  records: QAResponse[];
  total: number;
  page: number;
  size: number;
}

// 问答反馈请求
export interface QAFeedbackRequest {
  feedback_score: number;
  feedback_comment?: string;
}

// 热门问题
export interface PopularQuestion {
  question: string;
  category: string;
  ask_count: number;
}

// 热门问题响应
export interface PopularQuestionsResponse {
  questions: PopularQuestion[];
}

// 问答API
export const qaAPI = {
  // 提问
  askQuestion: (data: QARequest): Promise<QAResponse> =>
    api.post('/qa/ask', data),

  // 获取问答历史
  getHistory: (params: {
    skip?: number;
    limit?: number;
    category?: string;
  } = {}): Promise<QAHistoryResponse> =>
    api.get('/qa/history', { params }),

  // 获取问答记录详情
  getRecord: (recordId: string): Promise<QAResponse> =>
    api.get(`/qa/${recordId}`),

  // 提交反馈
  submitFeedback: (recordId: string, data: QAFeedbackRequest): Promise<void> =>
    api.post(`/qa/${recordId}/feedback`, data),

  // 获取热门问题
  getPopularQuestions: (limit: number = 10): Promise<PopularQuestionsResponse> =>
    api.get('/qa/popular/questions', { params: { limit } }),
};
