/**
 * 全局样式
 */

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

/* 响应式断点 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 8px;
}

.p-2 {
  padding: 16px;
}

.p-3 {
  padding: 24px;
}

/* 响应式隐藏类 */
@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .visible-xs {
    display: none !important;
  }
}

@media (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .visible-sm {
    display: none !important;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 移动端布局调整 */
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 1000;
  }
  
  .ant-layout-sider-collapsed {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
  }
  
  /* 移动端卡片间距 */
  .ant-card {
    margin-bottom: 16px;
  }
  
  /* 移动端表格滚动 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  /* 移动端按钮组 */
  .ant-btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  /* 移动端表单 */
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  /* 移动端统计卡片 */
  .ant-statistic {
    text-align: center;
  }
  
  .ant-statistic-title {
    font-size: 12px;
  }
  
  .ant-statistic-content {
    font-size: 20px;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .ant-col-md-12 {
    width: 50%;
  }
  
  .ant-col-md-8 {
    width: 33.333333%;
  }
  
  .ant-col-md-6 {
    width: 25%;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .ant-layout-content {
    padding: 24px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 渐入动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义主题色 */
.theme-primary {
  color: #1890ff;
}

.theme-success {
  color: #52c41a;
}

.theme-warning {
  color: #faad14;
}

.theme-error {
  color: #f5222d;
}

/* 无数据状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-header {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}
