/**
 * 用户相关类型定义
 */

// 用户类型
export type UserType = 'individual' | 'enterprise' | 'lawyer';

// 用户状态
export type UserStatus = 'active' | 'inactive' | 'suspended';

// 用户基础信息
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  phone?: string;
  user_type: UserType;
  status: UserStatus;
  email_verified: boolean;
  phone_verified: boolean;
  created_at: string;
  updated_at?: string;
  last_login_at?: string;
}

// 用户配置
export interface UserProfile {
  id: string;
  user_id: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  specialization?: string;
  license_number?: string;
  company_name?: string;
  company_position?: string;
  preferences: Record<string, any>;
  total_questions: string;
  total_contracts: string;
  total_documents: string;
  created_at: string;
  updated_at: string;
}

// 包含配置的用户信息
export interface UserWithProfile extends User {
  profile?: UserProfile;
}

// 用户更新请求
export interface UserUpdateRequest {
  full_name?: string;
  phone?: string;
}

// 用户密码更新请求
export interface UserPasswordUpdateRequest {
  current_password: string;
  new_password: string;
}

// 用户配置更新请求
export interface UserProfileUpdateRequest {
  avatar_url?: string;
  bio?: string;
  location?: string;
  specialization?: string;
  license_number?: string;
  company_name?: string;
  company_position?: string;
  preferences?: Record<string, any>;
}

// 用户统计信息
export interface UserStatistics {
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
  user_type_distribution: Record<string, number>;
  status_distribution: Record<string, number>;
}

// 用户会话
export interface UserSession {
  id: string;
  session_token: string;
  expires_at: string;
  ip_address?: string;
  user_agent?: string;
  device_info: Record<string, any>;
  is_active: boolean;
  created_at: string;
  last_accessed_at?: string;
}

// 用户活动日志
export interface UserActivityLog {
  id: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details: Record<string, any>;
  ip_address?: string;
  created_at: string;
}
