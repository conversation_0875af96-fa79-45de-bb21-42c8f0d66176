{"name": "ai-legal-assistant-web", "version": "1.0.0", "description": "AI法律助手Web前端", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "axios": "^1.6.0", "react-query": "^3.39.0", "react-hook-form": "^7.47.0", "react-router-dom": "^6.17.0", "antd": "^5.11.0", "@ant-design/icons": "^5.2.0", "styled-components": "^6.1.0", "dayjs": "^1.11.0", "lodash": "^4.17.21", "classnames": "^2.3.2", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/lodash": "^4.14.0", "@types/js-cookie": "^3.0.0", "@types/styled-components": "^5.1.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "prettier": "^3.0.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}