# AI法律助手 GitLab CI/CD 配置

stages:
  - validate
  - test
  - build
  - security
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.cache/npm"

# 缓存配置
.cache_template: &cache_template
  cache:
    key: "$CI_COMMIT_REF_SLUG"
    paths:
      - .cache/pip
      - .cache/npm
      - backend/venv/
      - frontend/web/node_modules/

# Python环境模板
.python_template: &python_template
  image: python:3.11-slim
  before_script:
    - cd backend
    - python -m venv venv
    - source venv/bin/activate
    - pip install --upgrade pip
    - pip install -r requirements.txt

# Node.js环境模板
.node_template: &node_template
  image: node:18-alpine
  before_script:
    - cd frontend/web
    - npm ci --cache .cache/npm --prefer-offline

# ==================== 验证阶段 ====================

# Python代码格式检查
python:format:
  <<: *python_template
  <<: *cache_template
  stage: validate
  script:
    - black --check .
    - isort --check-only .
  only:
    - merge_requests
    - main
    - develop

# Python代码规范检查
python:lint:
  <<: *python_template
  <<: *cache_template
  stage: validate
  script:
    - flake8 .
  only:
    - merge_requests
    - main
    - develop

# Python类型检查
python:typecheck:
  <<: *python_template
  <<: *cache_template
  stage: validate
  script:
    - mypy .
  allow_failure: true
  only:
    - merge_requests
    - main
    - develop

# 前端代码检查
frontend:lint:
  <<: *node_template
  <<: *cache_template
  stage: validate
  script:
    - npm run lint
    - npm run type-check
  only:
    - merge_requests
    - main
    - develop

# ==================== 测试阶段 ====================

# Python单元测试
python:test:
  <<: *python_template
  <<: *cache_template
  stage: test
  services:
    - postgres:15
    - redis:7-alpine
    - mongo:6.0
  variables:
    POSTGRES_DB: test_legal_assistant
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    DATABASE_URL: **************************************************/test_legal_assistant
    REDIS_URL: redis://redis:6379
    MONGODB_URL: mongodb://mongo:27017/test_legal_db
  script:
    - pytest --cov=. --cov-report=xml --cov-report=term
  coverage: '/TOTAL.+ ([0-9]{1,3}%)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage.xml
    expire_in: 1 week
  only:
    - merge_requests
    - main
    - develop

# 前端单元测试
frontend:test:
  <<: *node_template
  <<: *cache_template
  stage: test
  script:
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: frontend/web/coverage/cobertura-coverage.xml
    expire_in: 1 week
  only:
    - merge_requests
    - main
    - develop

# ==================== 构建阶段 ====================

# 后端Docker镜像构建
backend:build:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_IMAGE_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - cd backend
    - docker build -t $DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_TAG
    - docker tag $DOCKER_IMAGE_TAG $CI_REGISTRY_IMAGE/backend:latest
    - docker push $CI_REGISTRY_IMAGE/backend:latest
  only:
    - main
    - develop

# 前端构建
frontend:build:
  <<: *node_template
  <<: *cache_template
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - frontend/web/dist/
    expire_in: 1 week
  only:
    - merge_requests
    - main
    - develop

# 前端Docker镜像构建
frontend:docker:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_IMAGE_TAG: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
  dependencies:
    - frontend:build
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - cd frontend/web
    - docker build -t $DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_TAG
    - docker tag $DOCKER_IMAGE_TAG $CI_REGISTRY_IMAGE/frontend:latest
    - docker push $CI_REGISTRY_IMAGE/frontend:latest
  only:
    - main
    - develop

# ==================== 安全检查阶段 ====================

# Python安全扫描
security:python:
  <<: *python_template
  <<: *cache_template
  stage: security
  script:
    - bandit -r . -f json -o bandit-report.json
  artifacts:
    reports:
      sast: bandit-report.json
    expire_in: 1 week
  allow_failure: true
  only:
    - merge_requests
    - main
    - develop

# 依赖安全扫描
security:dependencies:
  image: python:3.11-slim
  stage: security
  before_script:
    - pip install safety
  script:
    - cd backend
    - safety check -r requirements.txt --json --output safety-report.json
  artifacts:
    reports:
      sast: backend/safety-report.json
    expire_in: 1 week
  allow_failure: true
  only:
    - merge_requests
    - main
    - develop

# Docker镜像安全扫描
security:docker:
  stage: security
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_IMAGE: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - apk add --no-cache curl
    - curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
  script:
    - trivy image --format json --output trivy-report.json $DOCKER_IMAGE
  artifacts:
    reports:
      sast: trivy-report.json
    expire_in: 1 week
  allow_failure: true
  dependencies:
    - backend:build
  only:
    - main
    - develop

# ==================== 部署阶段 ====================

# 开发环境部署
deploy:development:
  stage: deploy
  image: alpine/helm:latest
  environment:
    name: development
    url: https://dev.ai-legal-assistant.com
  before_script:
    - apk add --no-cache curl
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - chmod +x kubectl
    - mv kubectl /usr/local/bin/
  script:
    - echo "部署到开发环境..."
    - kubectl config use-context development
    - helm upgrade --install ai-legal-assistant ./deployment/helm/ai-legal-assistant
        --namespace development
        --set image.backend.tag=$CI_COMMIT_SHA
        --set image.frontend.tag=$CI_COMMIT_SHA
        --set environment=development
  dependencies:
    - backend:build
    - frontend:docker
  only:
    - develop
  when: manual

# 生产环境部署
deploy:production:
  stage: deploy
  image: alpine/helm:latest
  environment:
    name: production
    url: https://ai-legal-assistant.com
  before_script:
    - apk add --no-cache curl
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - chmod +x kubectl
    - mv kubectl /usr/local/bin/
  script:
    - echo "部署到生产环境..."
    - kubectl config use-context production
    - helm upgrade --install ai-legal-assistant ./deployment/helm/ai-legal-assistant
        --namespace production
        --set image.backend.tag=$CI_COMMIT_SHA
        --set image.frontend.tag=$CI_COMMIT_SHA
        --set environment=production
        --set replicas.backend=3
        --set replicas.frontend=2
  dependencies:
    - backend:build
    - frontend:docker
  only:
    - main
  when: manual

# ==================== 通知阶段 ====================

# 部署成功通知
notify:success:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"✅ AI法律助手部署成功！\n分支: $CI_COMMIT_REF_NAME\n提交: $CI_COMMIT_SHA\n环境: $CI_ENVIRONMENT_NAME\"}" \
      $SLACK_WEBHOOK_URL
  when: on_success
  only:
    - main
    - develop

# 部署失败通知
notify:failure:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"❌ AI法律助手部署失败！\n分支: $CI_COMMIT_REF_NAME\n提交: $CI_COMMIT_SHA\n流水线: $CI_PIPELINE_URL\"}" \
      $SLACK_WEBHOOK_URL
  when: on_failure
  only:
    - main
    - develop
