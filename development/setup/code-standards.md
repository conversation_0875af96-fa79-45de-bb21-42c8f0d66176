# 代码规范和质量检查工具配置

## 1. Python后端代码规范

### 1.1 Black代码格式化配置

```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
```

### 1.2 Flake8代码检查配置

```ini
# .flake8
[flake8]
max-line-length = 88
extend-ignore = E203, E266, E501, W503, F403, F401
max-complexity = 18
select = B,C,E,F,W,T4,B9
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    migrations,
    .pytest_cache
```

### 1.3 isort导入排序配置

```toml
# pyproject.toml
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "services", "shared"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
```

### 1.4 MyPy类型检查配置

```ini
# mypy.ini
[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

[mypy-tests.*]
disallow_untyped_defs = False
```

## 2. JavaScript/TypeScript前端代码规范

### 2.1 ESLint配置

```json
// .eslintrc.json
{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": [
    "react",
    "@typescript-eslint",
    "jsx-a11y",
    "import"
  ],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ]
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
```

### 2.2 Prettier配置

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

```
# .prettierignore
node_modules
dist
build
coverage
.next
.nuxt
.vuepress/dist
.serverless
.fusebox
.dynamodb
.tern-port
```

### 2.3 TypeScript配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/utils/*": ["utils/*"],
      "@/hooks/*": ["hooks/*"],
      "@/types/*": ["types/*"]
    }
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}
```

## 3. 代码质量检查脚本

### 3.1 Python代码检查脚本

```bash
#!/bin/bash
# scripts/check-python.sh

echo "🐍 Python代码质量检查..."

# 激活虚拟环境
source backend/venv/bin/activate

# Black格式化检查
echo "检查代码格式化..."
black --check backend/

# isort导入排序检查
echo "检查导入排序..."
isort --check-only backend/

# Flake8代码规范检查
echo "检查代码规范..."
flake8 backend/

# MyPy类型检查
echo "检查类型注解..."
mypy backend/

# 安全检查
echo "检查安全漏洞..."
bandit -r backend/ -f json -o reports/bandit-report.json

echo "✅ Python代码检查完成"
```

### 3.2 前端代码检查脚本

```bash
#!/bin/bash
# scripts/check-frontend.sh

echo "⚛️ 前端代码质量检查..."

cd frontend/web

# ESLint检查
echo "检查代码规范..."
npm run lint

# TypeScript类型检查
echo "检查TypeScript类型..."
npm run type-check

# Prettier格式检查
echo "检查代码格式..."
npm run format:check

# 构建检查
echo "检查构建..."
npm run build

echo "✅ 前端代码检查完成"
```

### 3.3 完整代码检查脚本

```bash
#!/bin/bash
# scripts/check-all.sh

echo "🔍 开始全面代码质量检查..."

# 创建报告目录
mkdir -p reports

# 检查Python代码
./scripts/check-python.sh

# 检查前端代码
./scripts/check-frontend.sh

# 生成代码质量报告
echo "📊 生成代码质量报告..."
echo "报告已生成到 reports/ 目录"

echo "🎉 代码质量检查完成！"
```

## 4. Git Hooks配置

### 4.1 Pre-commit Hook

```bash
#!/bin/bash
# .git/hooks/pre-commit

echo "🔍 Pre-commit检查..."

# 检查Python代码
if [ -d "backend" ]; then
    echo "检查Python代码..."
    cd backend
    source venv/bin/activate
    black --check . || exit 1
    flake8 . || exit 1
    cd ..
fi

# 检查前端代码
if [ -d "frontend/web" ]; then
    echo "检查前端代码..."
    cd frontend/web
    npm run lint || exit 1
    npm run type-check || exit 1
    cd ../..
fi

echo "✅ Pre-commit检查通过"
```

### 4.2 Pre-push Hook

```bash
#!/bin/bash
# .git/hooks/pre-push

echo "🚀 Pre-push检查..."

# 运行所有测试
echo "运行测试..."
./scripts/run-tests.sh || exit 1

echo "✅ Pre-push检查通过"
```

## 5. IDE集成配置

### 5.1 VS Code设置

```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.sortImports.args": ["--profile", "black"],
  
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  },
  
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  
  "files.exclude": {
    "**/__pycache__": true,
    "**/node_modules": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/venv": true
  }
}
```

### 5.2 VS Code任务配置

```json
// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Python: Format Code",
      "type": "shell",
      "command": "black",
      "args": ["backend/"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "Frontend: Lint",
      "type": "shell",
      "command": "npm",
      "args": ["run", "lint"],
      "options": {
        "cwd": "${workspaceFolder}/frontend/web"
      },
      "group": "build"
    },
    {
      "label": "Check All Code",
      "type": "shell",
      "command": "./scripts/check-all.sh",
      "group": "test"
    }
  ]
}
```

## 6. 持续集成配置

### 6.1 代码质量检查工作流

```yaml
# .github/workflows/code-quality.yml
name: Code Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  python-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install black flake8 mypy bandit
    
    - name: Check code formatting
      run: black --check backend/
    
    - name: Check code style
      run: flake8 backend/
    
    - name: Check types
      run: mypy backend/
    
    - name: Security check
      run: bandit -r backend/

  frontend-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: |
        cd frontend/web
        npm ci
    
    - name: Lint check
      run: |
        cd frontend/web
        npm run lint
    
    - name: Type check
      run: |
        cd frontend/web
        npm run type-check
    
    - name: Build check
      run: |
        cd frontend/web
        npm run build
```

## 使用说明

1. 安装代码检查工具：
   ```bash
   # Python工具
   pip install black flake8 mypy isort bandit
   
   # 前端工具
   npm install -D eslint prettier @typescript-eslint/parser @typescript-eslint/eslint-plugin
   ```

2. 设置Git Hooks：
   ```bash
   chmod +x .git/hooks/pre-commit
   chmod +x .git/hooks/pre-push
   ```

3. 运行代码检查：
   ```bash
   ./scripts/check-all.sh
   ```

代码规范和检查工具配置完成！确保所有代码都符合统一的质量标准。
