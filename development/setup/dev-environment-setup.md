# AI法律助手开发环境配置指南

## 开发工具链配置

### 1. 必需软件安装

#### 1.1 基础开发工具
```bash
# Git版本控制
sudo apt-get install git

# Node.js 18+ (前端开发)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Python 3.11+ (后端开发)
sudo apt-get install python3.11 python3.11-pip python3.11-venv

# Docker和Docker Compose
sudo apt-get install docker.io docker-compose
sudo usermod -aG docker $USER
```

#### 1.2 IDE配置推荐

**VS Code扩展列表**：
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode-remote.remote-containers",
    "ms-vscode.docker",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-json"
  ]
}
```

### 2. 项目结构初始化

```bash
# 创建项目根目录
mkdir ai-legal-assistant
cd ai-legal-assistant

# 创建主要目录结构
mkdir -p {backend/{services,shared,gateway},frontend/{web,mobile,admin},data,deployment/{docker,kubernetes,scripts},tests/{unit,integration,e2e},docs}

# 初始化Git仓库
git init
git remote add origin https://git.atjog.com/aier/ai-legal-assistant.git
```

### 3. 开发环境变量配置

创建环境变量模板：
```bash
# .env.example
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/legal_assistant
MONGODB_URL=mongodb://localhost:27017/legal_db
REDIS_URL=redis://localhost:6379
ELASTICSEARCH_URL=http://localhost:9200

# JWT配置
JWT_SECRET=your-secret-key-here
JWT_EXPIRE_HOURS=24

# 第三方服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 开发环境标识
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
```

### 4. Docker开发环境

创建开发环境Docker配置：
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: legal_assistant
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:8.0.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  elasticsearch_data:
```

### 5. 开发工具脚本

```bash
#!/bin/bash
# scripts/dev-setup.sh

echo "🚀 AI法律助手开发环境初始化..."

# 检查必需软件
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "检查必需软件..."
check_command git
check_command node
check_command python3
check_command docker
check_command docker-compose

# 复制环境变量文件
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ 环境变量文件已创建，请根据需要修改 .env 文件"
fi

# 启动开发数据库
echo "启动开发数据库..."
docker-compose -f docker-compose.dev.yml up -d

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

echo "🎉 开发环境配置完成！"
echo "📝 下一步："
echo "   1. 修改 .env 文件中的配置"
echo "   2. 运行 'npm install' 安装前端依赖"
echo "   3. 运行 'pip install -r requirements.txt' 安装后端依赖"
```

### 6. Git配置和规范

```bash
# .gitignore
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.pytest_cache/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# IDE
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Database
*.db
*.sqlite

# Build outputs
dist/
build/
*.egg-info/
```

Git提交规范：
```bash
# .gitmessage
# 提交类型(范围): 简短描述

# 详细描述（可选）

# 相关问题（可选）
# Closes #123
# Fixes #456

# 提交类型说明：
# feat: 新功能
# fix: 修复bug
# docs: 文档更新
# style: 代码格式调整
# refactor: 代码重构
# test: 测试相关
# chore: 构建过程或辅助工具的变动
```

### 7. 开发服务器启动脚本

```bash
#!/bin/bash
# scripts/start-dev.sh

echo "🚀 启动AI法律助手开发服务器..."

# 启动数据库服务
echo "启动数据库服务..."
docker-compose -f docker-compose.dev.yml up -d

# 启动后端服务
echo "启动后端服务..."
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# 启动前端服务
echo "启动前端服务..."
cd ../frontend/web
npm install
npm run dev &
FRONTEND_PID=$!

echo "✅ 开发服务器已启动"
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"

# 等待用户输入停止服务
read -p "按回车键停止所有服务..."

# 停止服务
kill $BACKEND_PID $FRONTEND_PID
docker-compose -f docker-compose.dev.yml down

echo "🛑 所有服务已停止"
```

## 配置完成检查清单

- [x] Git版本控制系统配置
- [x] Node.js和Python开发环境
- [x] Docker容器化环境
- [x] VS Code IDE扩展配置
- [x] 项目目录结构创建
- [x] 环境变量模板配置
- [x] 开发数据库Docker配置
- [x] Git规范和忽略文件配置
- [x] 开发脚本工具创建

## 使用说明

1. 运行 `chmod +x scripts/*.sh` 给脚本执行权限
2. 执行 `./scripts/dev-setup.sh` 初始化开发环境
3. 修改 `.env` 文件中的配置参数
4. 运行 `./scripts/start-dev.sh` 启动开发服务器

开发环境配置完成！团队成员可以使用统一的开发环境进行协作开发。
