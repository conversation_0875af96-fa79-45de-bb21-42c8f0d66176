{"name": "ai-legal-assistant", "version": "1.0.0", "description": "AI法律助手应用", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && uvicorn main:app --reload --host 0.0.0.0 --port 8000", "dev:frontend": "cd frontend/web && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend/web && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && python -m pytest", "test:frontend": "cd frontend/web && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && flake8 .", "lint:frontend": "cd frontend/web && npm run lint", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && black . && isort .", "format:frontend": "cd frontend/web && npm run format", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && mypy .", "type-check:frontend": "cd frontend/web && npm run type-check", "setup": "./scripts/dev-setup.sh", "start-dev": "./scripts/start-dev.sh", "check-all": "./scripts/check-all.sh"}, "keywords": ["ai", "legal", "assistant", "law", "nlp", "<PERSON><PERSON><PERSON>", "react"], "author": "AI法律助手开发团队", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}