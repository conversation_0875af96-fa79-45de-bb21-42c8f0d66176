"""
性能监控工具
"""

import time
import psutil
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics_history: List[Dict[str, Any]] = []
        self.start_time = time.time()
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used = disk.used / (1024**3)  # GB
            disk_total = disk.total / (1024**3)  # GB
            
            # 网络IO
            network = psutil.net_io_counters()
            
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count
                },
                "memory": {
                    "percent": memory_percent,
                    "used_gb": round(memory_used, 2),
                    "total_gb": round(memory_total, 2)
                },
                "disk": {
                    "percent": disk_percent,
                    "used_gb": round(disk_used, 2),
                    "total_gb": round(disk_total, 2)
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            }
            
            # 保存到历史记录
            self.metrics_history.append(metrics)
            
            # 只保留最近1小时的数据
            cutoff_time = datetime.now() - timedelta(hours=1)
            self.metrics_history = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {}
        
        try:
            recent_metrics = self.metrics_history[-10:]  # 最近10个数据点
            
            # 计算平均值
            avg_cpu = sum(m["cpu"]["percent"] for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m["memory"]["percent"] for m in recent_metrics) / len(recent_metrics)
            avg_disk = sum(m["disk"]["percent"] for m in recent_metrics) / len(recent_metrics)
            
            # 计算最大值
            max_cpu = max(m["cpu"]["percent"] for m in recent_metrics)
            max_memory = max(m["memory"]["percent"] for m in recent_metrics)
            max_disk = max(m["disk"]["percent"] for m in recent_metrics)
            
            # 运行时间
            uptime_seconds = time.time() - self.start_time
            uptime_hours = uptime_seconds / 3600
            
            return {
                "uptime_hours": round(uptime_hours, 2),
                "averages": {
                    "cpu_percent": round(avg_cpu, 2),
                    "memory_percent": round(avg_memory, 2),
                    "disk_percent": round(avg_disk, 2)
                },
                "peaks": {
                    "cpu_percent": round(max_cpu, 2),
                    "memory_percent": round(max_memory, 2),
                    "disk_percent": round(max_disk, 2)
                },
                "data_points": len(self.metrics_history),
                "last_updated": recent_metrics[-1]["timestamp"] if recent_metrics else None
            }
            
        except Exception as e:
            logger.error(f"生成性能摘要失败: {e}")
            return {}
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        
        if not self.metrics_history:
            return alerts
        
        latest = self.metrics_history[-1]
        
        # CPU告警
        if latest["cpu"]["percent"] > 80:
            alerts.append({
                "type": "cpu_high",
                "level": "warning" if latest["cpu"]["percent"] < 90 else "critical",
                "message": f"CPU使用率过高: {latest['cpu']['percent']:.1f}%",
                "value": latest["cpu"]["percent"],
                "threshold": 80
            })
        
        # 内存告警
        if latest["memory"]["percent"] > 85:
            alerts.append({
                "type": "memory_high",
                "level": "warning" if latest["memory"]["percent"] < 95 else "critical",
                "message": f"内存使用率过高: {latest['memory']['percent']:.1f}%",
                "value": latest["memory"]["percent"],
                "threshold": 85
            })
        
        # 磁盘告警
        if latest["disk"]["percent"] > 90:
            alerts.append({
                "type": "disk_high",
                "level": "warning" if latest["disk"]["percent"] < 95 else "critical",
                "message": f"磁盘使用率过高: {latest['disk']['percent']:.1f}%",
                "value": latest["disk"]["percent"],
                "threshold": 90
            })
        
        return alerts


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    metrics = performance_monitor.collect_system_metrics()
    summary = performance_monitor.get_performance_summary()
    alerts = performance_monitor.check_alerts()
    
    return {
        "status": "healthy" if not alerts else "warning" if all(a["level"] == "warning" for a in alerts) else "critical",
        "current_metrics": metrics,
        "summary": summary,
        "alerts": alerts,
        "timestamp": datetime.now().isoformat()
    }
