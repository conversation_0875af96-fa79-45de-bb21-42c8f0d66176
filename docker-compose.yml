# AI法律助手开发环境Docker Compose配置
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-legal-postgres-dev
    environment:
      - POSTGRES_DB=ai_legal_db
      - POSTGRES_USER=ai_legal_user
      - POSTGRES_PASSWORD=dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ai-legal-network

  # MongoDB数据库
  mongodb:
    image: mongo:6.0
    container_name: ai-legal-mongodb-dev
    environment:
      - MONGO_INITDB_ROOT_USERNAME=ai_legal_user
      - MONGO_INITDB_ROOT_PASSWORD=dev_password
      - MONGO_INITDB_DATABASE=ai_legal_db
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - ai-legal-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai-legal-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-legal-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ai-legal-elasticsearch-dev
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - ai-legal-network

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  ai-legal-network:
    driver: bridge
