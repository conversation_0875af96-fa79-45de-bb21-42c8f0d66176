# AI法律助手应用系统设计文档

## 1. 系统架构设计

### 1.1 整体架构概述
AI法律助手应用采用微服务架构，分为前端展示层、API网关层、业务服务层、数据访问层和基础设施层。

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Web前端       │   移动端App     │      管理后台           │
│   (React)       │   (React Native)│     (Vue.js)           │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层                                │
├─────────────────────────────────────────────────────────────┤
│  负载均衡 │ 身份认证 │ 限流控制 │ 日志监控 │ API版本管理    │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层                                │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ AI问答服务  │ 案例检索服务│ 合同工具服务│ 文书工具服务    │
├─────────────┼─────────────┼─────────────┼─────────────────┤
│纠纷解决服务 │ 用户管理服务│ 通知服务    │ 数据分析服务    │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层                                │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ 关系数据库  │ 文档数据库  │ 搜索引擎    │ 缓存系统        │
│ (PostgreSQL)│ (MongoDB)   │(Elasticsearch)│ (Redis)       │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                  基础设施层                                 │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ 容器编排    │ 消息队列    │ 文件存储    │ 监控告警        │
│(Kubernetes) │ (RabbitMQ)  │ (MinIO)     │ (Prometheus)    │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

### 1.2 技术栈选择

#### 1.2.1 前端技术栈
- **Web前端**: React 18 + TypeScript + Ant Design
- **移动端**: React Native + TypeScript
- **管理后台**: Vue.js 3 + TypeScript + Element Plus
- **状态管理**: Redux Toolkit (React) / Pinia (Vue)
- **HTTP客户端**: Axios
- **构建工具**: Vite

#### 1.2.2 后端技术栈
- **开发语言**: Python 3.11 + FastAPI
- **异步框架**: asyncio + uvicorn
- **ORM框架**: SQLAlchemy 2.0 + Alembic
- **API文档**: OpenAPI 3.0 + Swagger UI
- **依赖注入**: FastAPI Depends
- **数据验证**: Pydantic v2

#### 1.2.3 数据存储
- **关系数据库**: PostgreSQL 15 (用户数据、业务数据)
- **文档数据库**: MongoDB 6.0 (法律文档、案例数据)
- **搜索引擎**: Elasticsearch 8.0 (全文搜索、案例检索)
- **缓存系统**: Redis 7.0 (会话缓存、查询缓存)
- **文件存储**: MinIO (文档文件、图片存储)

#### 1.2.4 基础设施
- **容器化**: Docker + Docker Compose
- **容器编排**: Kubernetes (生产环境)
- **消息队列**: RabbitMQ (异步任务处理)
- **监控系统**: Prometheus + Grafana
- **日志系统**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **CI/CD**: GitLab CI/CD

### 1.3 服务拆分策略

#### 1.3.1 核心业务服务
1. **用户服务 (user-service)**
   - 用户注册、登录、权限管理
   - 用户配置文件管理
   - 用户行为统计

2. **AI问答服务 (qa-service)**
   - 自然语言处理
   - 法律知识库查询
   - 智能问答逻辑

3. **案例检索服务 (case-service)**
   - 案例数据管理
   - 智能搜索算法
   - 案例分析和推荐

4. **合同工具服务 (contract-service)**
   - 合同模板管理
   - 合同智能审查
   - 风险评估算法

5. **文书工具服务 (document-service)**
   - 文书模板管理
   - 文书自动生成
   - 格式校验和转换

6. **纠纷解决服务 (dispute-service)**
   - 纠纷类型识别
   - 解决流程指导
   - 资源推荐系统

#### 1.3.2 支撑服务
1. **通知服务 (notification-service)**
   - 邮件通知
   - 短信通知
   - 站内消息

2. **文件服务 (file-service)**
   - 文件上传下载
   - 文件格式转换
   - 文件安全扫描

3. **数据分析服务 (analytics-service)**
   - 用户行为分析
   - 业务数据统计
   - 报表生成

## 2. 数据库设计

### 2.1 关系数据库设计 (PostgreSQL)

#### 2.1.1 用户相关表

```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    user_type VARCHAR(20) DEFAULT 'individual', -- individual, enterprise, lawyer
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, suspended
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户配置表
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    avatar_url VARCHAR(255),
    bio TEXT,
    location VARCHAR(100),
    specialization VARCHAR(100), -- 专业领域（律师用户）
    license_number VARCHAR(50), -- 执业证号（律师用户）
    company_name VARCHAR(100), -- 公司名称（企业用户）
    preferences JSONB, -- 用户偏好设置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.1.2 业务数据表

```sql
-- 问答记录表
CREATE TABLE qa_records (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    question TEXT NOT NULL,
    answer TEXT,
    category VARCHAR(50), -- 法律领域分类
    confidence_score DECIMAL(3,2), -- AI回答置信度
    feedback_score INTEGER, -- 用户反馈评分 1-5
    status VARCHAR(20) DEFAULT 'completed', -- pending, completed, failed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 合同审查记录表
CREATE TABLE contract_reviews (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    contract_name VARCHAR(200),
    file_path VARCHAR(500),
    contract_type VARCHAR(50),
    risk_level VARCHAR(20), -- low, medium, high
    risk_points JSONB, -- 风险点详情
    suggestions JSONB, -- 修改建议
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文书生成记录表
CREATE TABLE document_generations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    document_type VARCHAR(50),
    template_id INTEGER,
    input_data JSONB, -- 用户输入的数据
    generated_content TEXT,
    file_path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户收藏表
CREATE TABLE user_favorites (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    item_type VARCHAR(20) NOT NULL, -- case, template, document
    item_id VARCHAR(50) NOT NULL,
    item_title VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, item_type, item_id)
);
```

### 2.2 文档数据库设计 (MongoDB)

#### 2.2.1 法律案例集合 (legal_cases)
```javascript
{
  "_id": ObjectId,
  "case_id": "string", // 案例唯一标识
  "title": "string", // 案例标题
  "court": "string", // 审理法院
  "case_type": "string", // 案件类型
  "case_category": "string", // 案件分类
  "judgment_date": Date, // 判决日期
  "parties": {
    "plaintiff": ["string"], // 原告
    "defendant": ["string"], // 被告
    "third_party": ["string"] // 第三人
  },
  "case_summary": "string", // 案例摘要
  "facts": "string", // 案件事实
  "court_opinion": "string", // 法院观点
  "judgment_result": "string", // 判决结果
  "legal_basis": ["string"], // 法律依据
  "keywords": ["string"], // 关键词
  "similar_cases": ["string"], // 相似案例ID
  "full_text": "string", // 完整文本
  "source_url": "string", // 数据源URL
  "created_at": Date,
  "updated_at": Date,
  "indexed_at": Date // 索引时间
}
```

#### 2.2.2 法律法规集合 (legal_regulations)
```javascript
{
  "_id": ObjectId,
  "regulation_id": "string", // 法规唯一标识
  "title": "string", // 法规标题
  "type": "string", // 法规类型：law, regulation, rule, interpretation
  "level": "string", // 法规层级：national, provincial, municipal
  "issuing_authority": "string", // 发布机关
  "issue_date": Date, // 发布日期
  "effective_date": Date, // 生效日期
  "status": "string", // 状态：effective, amended, repealed
  "chapters": [
    {
      "chapter_number": "string",
      "chapter_title": "string",
      "articles": [
        {
          "article_number": "string",
          "article_content": "string",
          "annotations": ["string"] // 条文注释
        }
      ]
    }
  ],
  "full_text": "string",
  "keywords": ["string"],
  "related_regulations": ["string"], // 相关法规ID
  "created_at": Date,
  "updated_at": Date
}
```

#### 2.2.3 合同模板集合 (contract_templates)
```javascript
{
  "_id": ObjectId,
  "template_id": "string",
  "name": "string", // 模板名称
  "category": "string", // 合同类别
  "description": "string", // 模板描述
  "template_content": "string", // 模板内容
  "variables": [
    {
      "name": "string", // 变量名
      "type": "string", // 数据类型
      "required": Boolean, // 是否必填
      "description": "string", // 变量说明
      "default_value": "string" // 默认值
    }
  ],
  "risk_clauses": [
    {
      "clause_text": "string", // 条款文本
      "risk_level": "string", // 风险等级
      "risk_description": "string", // 风险说明
      "suggestions": ["string"] // 修改建议
    }
  ],
  "usage_count": Number, // 使用次数
  "rating": Number, // 用户评分
  "tags": ["string"],
  "created_by": "string", // 创建者
  "created_at": Date,
  "updated_at": Date
}
```

### 2.3 搜索引擎设计 (Elasticsearch)

#### 2.3.1 案例搜索索引 (legal_cases_index)
```json
{
  "mappings": {
    "properties": {
      "case_id": {"type": "keyword"},
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "court": {"type": "keyword"},
      "case_type": {"type": "keyword"},
      "case_category": {"type": "keyword"},
      "judgment_date": {"type": "date"},
      "parties": {
        "properties": {
          "plaintiff": {"type": "text", "analyzer": "ik_max_word"},
          "defendant": {"type": "text", "analyzer": "ik_max_word"}
        }
      },
      "case_summary": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "facts": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "court_opinion": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "legal_basis": {"type": "keyword"},
      "keywords": {"type": "keyword"},
      "full_text": {
        "type": "text",
        "analyzer": "ik_max_word"
      }
    }
  }
}
```

## 3. API设计

### 3.1 API设计原则
- 遵循RESTful设计规范
- 使用标准HTTP状态码
- 统一的响应格式
- 版本控制策略
- 完善的错误处理
- API文档自动生成

### 3.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### 3.3 核心API接口设计

#### 3.3.1 用户认证API
```
POST /api/v1/auth/register     # 用户注册
POST /api/v1/auth/login        # 用户登录
POST /api/v1/auth/logout       # 用户登出
POST /api/v1/auth/refresh      # 刷新令牌
GET  /api/v1/auth/profile      # 获取用户信息
PUT  /api/v1/auth/profile      # 更新用户信息
```

#### 3.3.2 AI问答API
```
POST /api/v1/qa/ask            # 提交问题
GET  /api/v1/qa/history        # 获取问答历史
POST /api/v1/qa/feedback       # 提交反馈
GET  /api/v1/qa/categories     # 获取问题分类
```

#### 3.3.3 案例检索API
```
GET  /api/v1/cases/search      # 搜索案例
GET  /api/v1/cases/{id}        # 获取案例详情
GET  /api/v1/cases/similar     # 获取相似案例
POST /api/v1/cases/analyze     # 案例分析
```

#### 3.3.4 合同工具API
```
GET  /api/v1/contracts/templates    # 获取合同模板
POST /api/v1/contracts/generate     # 生成合同
POST /api/v1/contracts/review       # 审查合同
GET  /api/v1/contracts/history      # 获取历史记录
```

#### 3.3.5 文书工具API
```
GET  /api/v1/documents/templates    # 获取文书模板
POST /api/v1/documents/generate     # 生成文书
GET  /api/v1/documents/history      # 获取历史记录
POST /api/v1/documents/validate     # 验证文书格式
```

## 4. 用户界面设计

### 4.1 设计原则
- **简洁直观**: 界面简洁明了，操作流程清晰
- **响应式设计**: 适配不同屏幕尺寸和设备
- **无障碍设计**: 支持键盘导航和屏幕阅读器
- **一致性**: 保持视觉和交互的一致性
- **用户反馈**: 及时的操作反馈和状态提示

### 4.2 页面结构设计

#### 4.2.1 主要页面布局
```
┌─────────────────────────────────────────────────────────────┐
│                        顶部导航栏                           │
├─────────────────────────────────────────────────────────────┤
│ Logo │ 导航菜单 │ 搜索框 │ 用户头像 │ 通知 │ 设置 │ 登出  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────┬───────────────────────────────────────────┐
│                 │                                           │
│   侧边导航栏    │              主内容区域                   │
│                 │                                           │
│ • AI法律答疑    │                                           │
│ • 案例检索      │                                           │
│ • 合同工具      │                                           │
│ • 文书工具      │                                           │
│ • 纠纷解决      │                                           │
│ • 我的收藏      │                                           │
│ • 历史记录      │                                           │
│                 │                                           │
└─────────────────┴───────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        底部信息栏                           │
├─────────────────────────────────────────────────────────────┤
│ 版权信息 │ 联系我们 │ 隐私政策 │ 服务条款 │ 帮助中心      │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2.2 核心功能页面设计

**AI法律答疑页面**
- 聊天式界面，支持多轮对话
- 问题输入框，支持语音输入
- 历史对话记录
- 相关法条和案例推荐
- 问题分类快捷选择

**案例检索页面**
- 高级搜索表单（关键词、法院、时间范围等）
- 搜索结果列表，支持排序和筛选
- 案例详情页面，包含案例分析
- 相似案例推荐
- 案例收藏和分享功能

**合同工具页面**
- 合同模板选择界面
- 合同信息填写表单
- 合同预览和编辑器
- 风险点标注和建议显示
- 合同下载和分享功能

### 4.3 移动端设计适配
- 底部导航栏设计
- 手势操作支持
- 触摸友好的按钮尺寸
- 简化的表单设计
- 离线功能支持

### 4.4 主题和样式系统
```css
/* 主色调 */
:root {
  --primary-color: #1890ff;      /* 主色 - 蓝色 */
  --secondary-color: #52c41a;    /* 辅助色 - 绿色 */
  --warning-color: #faad14;      /* 警告色 - 橙色 */
  --error-color: #f5222d;        /* 错误色 - 红色 */
  --text-primary: #262626;       /* 主要文本 */
  --text-secondary: #8c8c8c;     /* 次要文本 */
  --background-color: #f5f5f5;   /* 背景色 */
  --border-color: #d9d9d9;       /* 边框色 */
}

/* 字体系统 */
.font-large { font-size: 18px; }
.font-medium { font-size: 16px; }
.font-small { font-size: 14px; }
.font-mini { font-size: 12px; }

/* 间距系统 */
.spacing-xs { margin: 4px; }
.spacing-sm { margin: 8px; }
.spacing-md { margin: 16px; }
.spacing-lg { margin: 24px; }
.spacing-xl { margin: 32px; }
```

## 5. 部署架构设计

### 5.1 环境规划
- **开发环境**: 本地开发和单元测试
- **测试环境**: 集成测试和用户验收测试
- **预生产环境**: 性能测试和压力测试
- **生产环境**: 正式服务环境

### 5.2 容器化部署

#### 5.2.1 Docker镜像构建
```dockerfile
# Python后端服务镜像
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 5.2.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # API网关
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api-gateway

  # API网关服务
  api-gateway:
    build: ./gateway
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

  # 用户服务
  user-service:
    build: ./services/user
    environment:
      - DATABASE_URL=************************************/userdb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  # AI问答服务
  qa-service:
    build: ./services/qa
    environment:
      - DATABASE_URL=************************************/qadb
      - MONGODB_URL=mongodb://mongo:27017/legal_db
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - postgres
      - mongodb
      - elasticsearch

  # 数据库服务
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=legal_assistant
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  mongodb:
    image: mongo:6.0
    volumes:
      - mongodb_data:/data/db

  elasticsearch:
    image: elasticsearch:8.0.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  mongodb_data:
  elasticsearch_data:
  redis_data:
```

### 5.3 Kubernetes部署配置

#### 5.3.1 命名空间和资源配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: legal-assistant

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: legal-assistant
data:
  DATABASE_URL: "************************************/legal_db"
  REDIS_URL: "redis://redis:6379"
  ELASTICSEARCH_URL: "http://elasticsearch:9200"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: legal-assistant
type: Opaque
data:
  db-password: cGFzc3dvcmQ=  # base64 encoded
  jwt-secret: c2VjcmV0a2V5  # base64 encoded
```

#### 5.3.2 服务部署配置
```yaml
# user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: legal-assistant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: legal-assistant/user-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: DATABASE_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: legal-assistant
spec:
  selector:
    app: user-service
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
```

### 5.4 监控和日志

#### 5.4.1 Prometheus监控配置
```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'legal-assistant-services'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - legal-assistant
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
```

#### 5.4.2 日志收集配置
```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*legal-assistant*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      format json
    </source>

    <match kubernetes.**>
      @type elasticsearch
      host elasticsearch.legal-assistant.svc.cluster.local
      port 9200
      index_name legal-assistant-logs
    </match>
```

## 6. 安全设计

### 6.1 身份认证和授权
- **JWT令牌认证**: 无状态的用户身份验证
- **RBAC权限模型**: 基于角色的访问控制
- **OAuth2集成**: 支持第三方登录
- **多因素认证**: 短信验证码、邮箱验证

### 6.2 数据安全
- **数据加密**: 敏感数据加密存储
- **传输加密**: HTTPS/TLS加密传输
- **数据脱敏**: 日志和监控数据脱敏
- **备份加密**: 数据备份加密存储

### 6.3 API安全
- **请求限流**: 防止API滥用
- **输入验证**: 严格的参数验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容转义

### 6.4 网络安全
- **防火墙配置**: 网络访问控制
- **DDoS防护**: 分布式拒绝服务攻击防护
- **WAF部署**: Web应用防火墙
- **安全扫描**: 定期安全漏洞扫描
