"""
问答模块测试用例
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from datetime import datetime

from app.main import app
from app.models.qa import QARecord, QAStatus
from app.services.qa import QAService


client = TestClient(app)


@pytest.fixture
def mock_qa_record():
    """模拟问答记录"""
    return QARecord(
        id="test-qa-id",
        user_id="test-user-id",
        question="什么是合同违约？",
        answer="合同违约是指当事人不履行合同义务或者履行合同义务不符合约定的行为。",
        category="contract",
        confidence_score=0.95,
        status=QAStatus.COMPLETED,
        created_at=datetime.now()
    )


class TestQAService:
    """问答服务测试"""
    
    @pytest.mark.asyncio
    async def test_ask_question_success(self, mock_qa_record):
        """测试提问成功"""
        question_data = {
            "question": "什么是合同违约？",
            "category": "contract"
        }
        
        with patch('app.services.qa.QAService.process_question') as mock_process:
            mock_process.return_value = mock_qa_record
            
            headers = {"Authorization": "Bearer valid-token"}
            response = client.post("/api/v1/qa/ask", json=question_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["question"] == "什么是合同违约？"
            assert data["answer"] is not None
            assert data["status"] == "completed"
    
    @pytest.mark.asyncio
    async def test_ask_empty_question(self):
        """测试空问题"""
        question_data = {
            "question": "",
            "category": "contract"
        }
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.post("/api/v1/qa/ask", json=question_data, headers=headers)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_ask_question_too_long(self):
        """测试问题过长"""
        question_data = {
            "question": "a" * 1001,  # 超过1000字符限制
            "category": "contract"
        }
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.post("/api/v1/qa/ask", json=question_data, headers=headers)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_get_qa_history(self, mock_qa_record):
        """测试获取问答历史"""
        mock_history = {
            "records": [mock_qa_record],
            "total": 1,
            "page": 1,
            "size": 20
        }
        
        with patch('app.services.qa.QAService.get_user_qa_history') as mock_get_history:
            mock_get_history.return_value = (mock_history["records"], mock_history["total"])
            
            headers = {"Authorization": "Bearer valid-token"}
            response = client.get("/api/v1/qa/history", headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 1
            assert len(data["records"]) == 1
    
    @pytest.mark.asyncio
    async def test_submit_feedback_success(self):
        """测试提交反馈成功"""
        feedback_data = {
            "feedback_score": 5,
            "feedback_comment": "回答很有帮助"
        }
        
        with patch('app.services.qa.QAService.submit_feedback') as mock_feedback:
            mock_feedback.return_value = True
            
            headers = {"Authorization": "Bearer valid-token"}
            response = client.post(
                "/api/v1/qa/test-qa-id/feedback", 
                json=feedback_data, 
                headers=headers
            )
            
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_submit_invalid_feedback_score(self):
        """测试无效反馈评分"""
        feedback_data = {
            "feedback_score": 6,  # 超出1-5范围
            "feedback_comment": "回答很有帮助"
        }
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.post(
            "/api/v1/qa/test-qa-id/feedback", 
            json=feedback_data, 
            headers=headers
        )
        
        assert response.status_code == 422


class TestQAAlgorithm:
    """问答算法测试"""
    
    def test_question_classification(self):
        """测试问题分类"""
        from app.services.qa import QAService
        
        qa_service = QAService(None)
        
        # 测试合同相关问题
        contract_question = "合同违约怎么办？"
        category = qa_service._classify_question(contract_question)
        assert category == "contract"
        
        # 测试劳动法相关问题
        labor_question = "公司不发工资怎么办？"
        category = qa_service._classify_question(labor_question)
        assert category == "labor"
        
        # 测试婚姻法相关问题
        family_question = "离婚财产怎么分割？"
        category = qa_service._classify_question(family_question)
        assert category == "family"
    
    def test_answer_generation(self):
        """测试答案生成"""
        from app.services.qa import QAService
        
        qa_service = QAService(None)
        
        question = "什么是合同？"
        # 模拟答案生成
        with patch.object(qa_service, '_generate_answer') as mock_generate:
            mock_generate.return_value = "合同是平等主体的自然人、法人、其他组织之间设立、变更、终止民事权利义务关系的协议。"
            
            answer = qa_service._generate_answer(question, "contract")
            assert "合同" in answer
            assert len(answer) > 10
    
    def test_confidence_calculation(self):
        """测试置信度计算"""
        from app.services.qa import QAService
        
        qa_service = QAService(None)
        
        # 测试高置信度问题
        high_confidence_question = "什么是合同违约？"
        confidence = qa_service._calculate_confidence(high_confidence_question, "contract")
        assert confidence >= 0.8
        
        # 测试低置信度问题
        low_confidence_question = "这个问题很奇怪"
        confidence = qa_service._calculate_confidence(low_confidence_question, "general")
        assert confidence <= 0.5


class TestQAIntegration:
    """问答集成测试"""
    
    @pytest.mark.asyncio
    async def test_complete_qa_flow(self):
        """测试完整问答流程"""
        # 1. 用户提问
        question_data = {
            "question": "合同纠纷如何解决？",
            "category": "contract"
        }
        
        # 2. 模拟问答处理
        mock_record = QARecord(
            id="integration-test-id",
            user_id="test-user-id",
            question=question_data["question"],
            answer="合同纠纷可以通过协商、调解、仲裁或诉讼等方式解决。",
            category="contract",
            confidence_score=0.9,
            status=QAStatus.COMPLETED
        )
        
        with patch('app.services.qa.QAService.process_question') as mock_process:
            mock_process.return_value = mock_record
            
            headers = {"Authorization": "Bearer valid-token"}
            response = client.post("/api/v1/qa/ask", json=question_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # 3. 验证返回结果
            assert data["question"] == question_data["question"]
            assert data["answer"] is not None
            assert data["category"] == "contract"
            assert data["confidence_score"] >= 0.8
            
            # 4. 提交反馈
            feedback_data = {
                "feedback_score": 4,
                "feedback_comment": "回答比较准确"
            }
            
            with patch('app.services.qa.QAService.submit_feedback') as mock_feedback:
                mock_feedback.return_value = True
                
                feedback_response = client.post(
                    f"/api/v1/qa/{data['id']}/feedback",
                    json=feedback_data,
                    headers=headers
                )
                
                assert feedback_response.status_code == 200


# 性能测试
class TestQAPerformance:
    """问答性能测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_questions(self):
        """测试并发问题处理"""
        import asyncio
        from app.services.qa import QAService
        
        qa_service = QAService(AsyncMock())
        
        # 模拟并发问题
        questions = [
            "什么是合同？",
            "如何解除合同？",
            "合同违约怎么办？",
            "合同纠纷如何处理？",
            "什么是要约？"
        ]
        
        # 并发处理问题
        tasks = []
        for question in questions:
            task = qa_service.process_question("test-user", question, "contract")
            tasks.append(task)
        
        # 等待所有任务完成
        with patch.object(qa_service, 'process_question') as mock_process:
            mock_process.return_value = QARecord(
                id="test-id",
                user_id="test-user",
                question="test",
                answer="test answer",
                category="contract",
                status=QAStatus.COMPLETED
            )
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 验证所有问题都得到处理
            assert len(results) == len(questions)
            for result in results:
                assert not isinstance(result, Exception)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
