"""
认证模块测试用例
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import AsyncMock, patch

from app.main import app
from app.core.database import get_db
from app.models.user import User
from app.services.auth import AuthService


# 测试客户端
client = TestClient(app)

# 模拟数据库会话
@pytest.fixture
async def mock_db():
    """模拟数据库会话"""
    mock_session = AsyncMock(spec=AsyncSession)
    return mock_session


# 模拟用户数据
@pytest.fixture
def mock_user():
    """模拟用户数据"""
    return User(
        id="test-user-id",
        username="testuser",
        email="<EMAIL>",
        full_name="测试用户",
        user_type="individual",
        status="active",
        email_verified=True,
        phone_verified=False
    )


class TestUserRegistration:
    """用户注册测试"""
    
    def test_register_success(self, mock_db, mock_user):
        """测试用户注册成功"""
        # 准备测试数据
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "full_name": "测试用户",
            "user_type": "individual"
        }
        
        # 模拟数据库操作
        with patch('app.services.auth.AuthService.register_user') as mock_register:
            mock_register.return_value = mock_user
            
            # 发送注册请求
            response = client.post("/api/v1/auth/register", json=register_data)
            
            # 验证响应
            assert response.status_code == 201
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
            assert "password" not in data
    
    def test_register_duplicate_username(self):
        """测试用户名重复注册"""
        register_data = {
            "username": "existinguser",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "user_type": "individual"
        }
        
        with patch('app.services.auth.AuthService.register_user') as mock_register:
            mock_register.side_effect = ValueError("用户名已存在")
            
            response = client.post("/api/v1/auth/register", json=register_data)
            
            assert response.status_code == 400
            assert "用户名已存在" in response.json()["detail"]
    
    def test_register_invalid_email(self):
        """测试无效邮箱格式"""
        register_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "TestPassword123!",
            "user_type": "individual"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        
        assert response.status_code == 422
    
    def test_register_weak_password(self):
        """测试弱密码"""
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "123",
            "user_type": "individual"
        }
        
        response = client.post("/api/v1/auth/register", json=register_data)
        
        assert response.status_code == 422


class TestUserLogin:
    """用户登录测试"""
    
    def test_login_success(self, mock_user):
        """测试登录成功"""
        login_data = {
            "username": "testuser",
            "password": "TestPassword123!"
        }
        
        mock_token_response = {
            "access_token": "mock-access-token",
            "refresh_token": "mock-refresh-token",
            "token_type": "bearer",
            "expires_in": 3600
        }
        
        with patch('app.services.auth.AuthService.authenticate_user') as mock_auth:
            mock_auth.return_value = mock_token_response
            
            response = client.post("/api/v1/auth/login/json", json=login_data)
            
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
            assert "refresh_token" in data
            assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self):
        """测试无效凭据"""
        login_data = {
            "username": "testuser",
            "password": "wrongpassword"
        }
        
        with patch('app.services.auth.AuthService.authenticate_user') as mock_auth:
            mock_auth.side_effect = ValueError("用户名或密码错误")
            
            response = client.post("/api/v1/auth/login/json", json=login_data)
            
            assert response.status_code == 401
            assert "用户名或密码错误" in response.json()["detail"]
    
    def test_login_inactive_user(self):
        """测试非活跃用户登录"""
        login_data = {
            "username": "inactiveuser",
            "password": "TestPassword123!"
        }
        
        with patch('app.services.auth.AuthService.authenticate_user') as mock_auth:
            mock_auth.side_effect = ValueError("用户账户已被禁用")
            
            response = client.post("/api/v1/auth/login/json", json=login_data)
            
            assert response.status_code == 401


class TestTokenOperations:
    """令牌操作测试"""
    
    def test_refresh_token_success(self):
        """测试刷新令牌成功"""
        refresh_data = {
            "refresh_token": "valid-refresh-token"
        }
        
        mock_response = {
            "access_token": "new-access-token",
            "token_type": "bearer",
            "expires_in": 3600
        }
        
        with patch('app.services.auth.AuthService.refresh_access_token') as mock_refresh:
            mock_refresh.return_value = mock_response
            
            response = client.post("/api/v1/auth/refresh", json=refresh_data)
            
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
    
    def test_refresh_token_invalid(self):
        """测试无效刷新令牌"""
        refresh_data = {
            "refresh_token": "invalid-refresh-token"
        }
        
        with patch('app.services.auth.AuthService.refresh_access_token') as mock_refresh:
            mock_refresh.side_effect = ValueError("无效的刷新令牌")
            
            response = client.post("/api/v1/auth/refresh", json=refresh_data)
            
            assert response.status_code == 401
    
    def test_logout_success(self):
        """测试登出成功"""
        headers = {"Authorization": "Bearer valid-access-token"}
        
        with patch('app.dependencies.auth.get_current_user') as mock_get_user:
            mock_get_user.return_value = {"id": "test-user-id"}
            
            response = client.post("/api/v1/auth/logout", headers=headers)
            
            assert response.status_code == 200


class TestUserProfile:
    """用户资料测试"""
    
    def test_get_current_user_success(self, mock_user):
        """测试获取当前用户信息成功"""
        headers = {"Authorization": "Bearer valid-access-token"}
        
        with patch('app.dependencies.auth.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            response = client.get("/api/v1/auth/me", headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["username"] == "testuser"
            assert data["email"] == "<EMAIL>"
    
    def test_get_current_user_unauthorized(self):
        """测试未授权访问用户信息"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401


# 运行测试的配置
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
