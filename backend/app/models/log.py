"""
日志相关数据模型
"""

import uuid
from sqlalchemy import Column, String, Text, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class SystemLog(Base):
    """系统日志表"""
    __tablename__ = "system_logs"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True,
        comment="用户ID"
    )
    
    # 操作信息
    action = Column(String(50), nullable=False, index=True, comment="操作类型")
    resource_type = Column(String(50), index=True, comment="资源类型")
    resource_id = Column(String(50), comment="资源ID")
    
    # 详细信息
    details = Column(JSONB, default=dict, comment="操作详情")
    
    # 客户端信息
    ip_address = Column(INET, comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    
    # 关系
    user = relationship("User", back_populates="system_logs")

    def __repr__(self):
        return f"<SystemLog(id={self.id}, action={self.action}, resource_type={self.resource_type})>"
