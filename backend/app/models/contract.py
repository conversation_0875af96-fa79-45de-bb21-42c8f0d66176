"""
合同相关数据模型
"""

import uuid
from sqlalchemy import Column, String, ForeignKey, DateTime, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class RiskLevel(str, enum.Enum):
    """风险等级枚举"""
    LOW = "low"  # 低风险
    MEDIUM = "medium"  # 中风险
    HIGH = "high"  # 高风险


class ContractStatus(str, enum.Enum):
    """合同处理状态枚举"""
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class ContractReview(Base):
    """合同审查记录表"""
    __tablename__ = "contract_reviews"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True,
        comment="用户ID"
    )
    
    # 合同信息
    contract_name = Column(String(200), comment="合同名称")
    file_path = Column(String(500), comment="文件路径")
    contract_type = Column(String(50), index=True, comment="合同类型")
    
    # 风险评估
    risk_level = Column(
        SQLEnum(RiskLevel),
        index=True,
        comment="风险等级"
    )
    risk_points = Column(JSONB, default=list, comment="风险点详情")
    suggestions = Column(JSONB, default=list, comment="修改建议")
    
    # 状态
    status = Column(
        SQLEnum(ContractStatus),
        default=ContractStatus.COMPLETED,
        nullable=False,
        index=True,
        comment="处理状态"
    )
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    user = relationship("User", back_populates="contract_reviews")

    def __repr__(self):
        return f"<ContractReview(id={self.id}, contract_name={self.contract_name}, risk_level={self.risk_level})>"
