"""
收藏相关数据模型
"""

import uuid
from sqlalchemy import Column, String, ForeignKey, DateTime, Enum as SQLEnum, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class ItemType(str, enum.Enum):
    """收藏项类型枚举"""
    CASE = "case"  # 案例
    TEMPLATE = "template"  # 模板
    DOCUMENT = "document"  # 文书
    QA = "qa"  # 问答


class UserFavorite(Base):
    """用户收藏表"""
    __tablename__ = "user_favorites"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    # 收藏项信息
    item_type = Column(
        SQLEnum(ItemType),
        nullable=False,
        index=True,
        comment="收藏项类型"
    )
    item_id = Column(String(50), nullable=False, comment="收藏项ID")
    item_title = Column(String(200), comment="收藏项标题")
    item_data = Column(JSONB, default=dict, comment="收藏项的额外数据")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    
    # 关系
    user = relationship("User", back_populates="favorites")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('user_id', 'item_type', 'item_id', name='uq_user_item'),
    )

    def __repr__(self):
        return f"<UserFavorite(id={self.id}, user_id={self.user_id}, item_type={self.item_type}, item_id={self.item_id})>"
