"""
问答相关数据模型
"""

import uuid
from typing import Optional
from sqlalchemy import Column, String, Text, Integer, Numeric, ForeignKey, DateTime, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class QAStatus(str, enum.Enum):
    """问答状态枚举"""
    PENDING = "pending"  # 待处理
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class QARecord(Base):
    """问答记录表"""
    __tablename__ = "qa_records"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True,
        comment="用户ID"
    )
    
    # 会话信息
    session_id = Column(String(255), index=True, comment="会话ID")
    
    # 问答内容
    question = Column(Text, nullable=False, comment="用户问题")
    answer = Column(Text, comment="AI回答")
    
    # 分类和评分
    category = Column(String(50), index=True, comment="法律领域分类")
    confidence_score = Column(Numeric(3, 2), comment="AI回答置信度")
    feedback_score = Column(Integer, comment="用户反馈评分 1-5")
    
    # 状态
    status = Column(
        SQLEnum(QAStatus),
        default=QAStatus.COMPLETED,
        nullable=False,
        index=True,
        comment="处理状态"
    )
    
    # 元数据
    metadata = Column(JSONB, default=dict, comment="额外元数据")
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    user = relationship("User", back_populates="qa_records")

    def __repr__(self):
        return f"<QARecord(id={self.id}, user_id={self.user_id}, category={self.category})>"
