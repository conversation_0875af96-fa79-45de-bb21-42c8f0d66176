"""
文书相关数据模型
"""

import uuid
from sqlalchemy import Column, String, Text, ForeignKey, DateTime, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class DocumentStatus(str, enum.Enum):
    """文书生成状态枚举"""
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class DocumentGeneration(Base):
    """文书生成记录表"""
    __tablename__ = "document_generations"

    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 外键
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True,
        comment="用户ID"
    )
    
    # 文书信息
    document_type = Column(String(50), index=True, comment="文书类型")
    template_id = Column(UUID(as_uuid=True), comment="模板ID")
    
    # 生成数据
    input_data = Column(JSONB, default=dict, comment="用户输入的数据")
    generated_content = Column(Text, comment="生成的内容")
    file_path = Column(String(500), comment="生成文件路径")
    
    # 状态
    status = Column(
        SQLEnum(DocumentStatus),
        default=DocumentStatus.COMPLETED,
        nullable=False,
        index=True,
        comment="生成状态"
    )
    
    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    user = relationship("User", back_populates="document_generations")

    def __repr__(self):
        return f"<DocumentGeneration(id={self.id}, document_type={self.document_type}, status={self.status})>"
