"""
AI问答API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, validator
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user, check_qa_rate_limit
from app.models.user import User
from app.services.qa import QAService
from app.schemas.qa import QARequest, QAResponse, QAHistoryResponse, QAFeedbackRequest

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/ask", response_model=QAResponse)
async def ask_question(
    qa_request: QARequest,
    current_user: User = Depends(check_qa_rate_limit),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """提问接口"""

    logger.info(f"用户提问: {current_user.username}")

    qa_service = QAService(db)
    qa_record = await qa_service.ask_question(
        user_id=current_user.id,
        question=qa_request.question,
        category=qa_request.category,
        session_id=qa_request.session_id
    )

    return QAResponse(
        id=qa_record.id,
        question=qa_record.question,
        answer=qa_record.answer,
        category=qa_record.category,
        confidence_score=qa_record.confidence_score,
        status=qa_record.status,
        created_at=qa_record.created_at
    )


@router.get("/history", response_model=QAHistoryResponse)
async def get_qa_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取问答历史"""

    qa_service = QAService(db)
    records, total = await qa_service.get_qa_history(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        category=category
    )

    return QAHistoryResponse(
        records=[QAResponse.from_orm(record) for record in records],
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{record_id}", response_model=QAResponse)
async def get_qa_record(
    record_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取问答记录详情"""

    from uuid import UUID
    from app.core.exceptions import NotFoundException

    try:
        record_uuid = UUID(record_id)
    except ValueError:
        raise NotFoundException("问答记录不存在")

    qa_service = QAService(db)
    record = await qa_service.get_qa_record(record_uuid, current_user.id)

    if not record:
        raise NotFoundException("问答记录不存在")

    return QAResponse.from_orm(record)


@router.post("/{record_id}/feedback")
async def submit_feedback(
    record_id: str,
    feedback: QAFeedbackRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """提交问答反馈"""

    from uuid import UUID
    from app.core.exceptions import NotFoundException

    try:
        record_uuid = UUID(record_id)
    except ValueError:
        raise NotFoundException("问答记录不存在")

    qa_service = QAService(db)
    success = await qa_service.submit_feedback(
        record_id=record_uuid,
        user_id=current_user.id,
        feedback_score=feedback.feedback_score,
        feedback_comment=feedback.feedback_comment
    )

    if not success:
        raise NotFoundException("问答记录不存在")

    return {"message": "反馈提交成功"}


@router.get("/popular/questions")
async def get_popular_questions(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取热门问题"""

    from app.schemas.qa import PopularQuestionsResponse, PopularQuestion

    qa_service = QAService(db)
    questions = await qa_service.get_popular_questions(limit)

    return PopularQuestionsResponse(
        questions=[PopularQuestion(**q) for q in questions]
    )
