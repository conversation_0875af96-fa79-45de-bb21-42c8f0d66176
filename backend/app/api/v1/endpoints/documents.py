"""
文书工具API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, Body
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user, check_document_rate_limit
from app.models.user import User
from app.services.documents import DocumentGenerationService
from app.schemas.documents import (
    DocumentGenerationRequest,
    DocumentGenerationResponse,
    DocumentTemplatesResponse,
    DocumentGenerationsResponse
)
from app.core.exceptions import NotFoundException

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/templates", response_model=DocumentTemplatesResponse)
async def get_document_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取文书模板列表"""

    document_service = DocumentGenerationService(db)
    templates, total = await document_service.get_document_templates(
        skip=skip,
        limit=limit,
        category=category
    )

    return DocumentTemplatesResponse(
        templates=templates,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.post("/generate", response_model=DocumentGenerationResponse)
async def generate_document(
    request: DocumentGenerationRequest,
    current_user: User = Depends(check_document_rate_limit),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """生成法律文书"""

    logger.info(f"用户生成文书: {current_user.username}, type: {request.document_type}")

    document_service = DocumentGenerationService(db)
    generation = await document_service.generate_document(
        user_id=current_user.id,
        document_type=request.document_type,
        template_id=request.template_id,
        input_data=request.input_data
    )

    return DocumentGenerationResponse.from_orm(generation)


@router.get("/generations", response_model=DocumentGenerationsResponse)
async def get_document_generations(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取文书生成记录"""

    document_service = DocumentGenerationService(db)
    generations, total = await document_service.get_document_generations(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )

    from app.schemas.documents import DocumentGenerationSummary

    return DocumentGenerationsResponse(
        generations=[DocumentGenerationSummary.from_orm(gen) for gen in generations],
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/generations/{generation_id}", response_model=DocumentGenerationResponse)
async def get_document_generation_detail(
    generation_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取文书生成详情"""

    from uuid import UUID

    try:
        generation_uuid = UUID(generation_id)
    except ValueError:
        raise NotFoundException("文书生成记录不存在")

    document_service = DocumentGenerationService(db)
    generation = await document_service.get_document_generation(generation_uuid, current_user.id)

    if not generation:
        raise NotFoundException("文书生成记录不存在")

    return DocumentGenerationResponse.from_orm(generation)


@router.get("/generations/{generation_id}/download")
async def download_document(
    generation_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """下载生成的文书"""

    from uuid import UUID
    import os

    try:
        generation_uuid = UUID(generation_id)
    except ValueError:
        raise NotFoundException("文书生成记录不存在")

    document_service = DocumentGenerationService(db)
    file_path = await document_service.download_document(generation_uuid, current_user.id)

    if not file_path or not os.path.exists(file_path):
        raise NotFoundException("文件不存在")

    filename = os.path.basename(file_path)
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )


@router.get("/templates/{template_id}")
async def get_document_template_detail(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取文书模板详情"""

    # TODO: 实现文书模板详情查询
    return {"template": None}
