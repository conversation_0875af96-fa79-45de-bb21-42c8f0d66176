"""
纠纷解决API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, validator
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.disputes import DisputeResolutionService
from app.schemas.disputes import (
    DisputeAnalysisRequest,
    DisputeAnalysisResponse,
    DisputeSolutionsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/analyze", response_model=DisputeAnalysisResponse)
async def analyze_dispute(
    request: DisputeAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """分析纠纷情况"""

    logger.info(f"用户分析纠纷: {current_user.username}")

    dispute_service = DisputeResolutionService(db)
    analysis_result = await dispute_service.analyze_dispute(
        user_id=current_user.id,
        dispute_description=request.dispute_description,
        dispute_type=request.dispute_type,
        amount_involved=request.amount_involved,
        parties_info=request.parties_info
    )

    return DisputeAnalysisResponse(**analysis_result)


@router.get("/solutions", response_model=DisputeSolutionsResponse)
async def get_dispute_solutions(
    dispute_type: str = Query(..., description="纠纷类型"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取纠纷解决方案"""

    dispute_service = DisputeResolutionService(db)
    solutions = await dispute_service.get_dispute_solutions(
        dispute_type=dispute_type,
        user_id=current_user.id
    )

    return DisputeSolutionsResponse(solutions=solutions)


@router.get("/types")
async def get_dispute_types(
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取支持的纠纷类型"""

    dispute_types = [
        {"code": "contract", "name": "合同纠纷", "description": "合同履行、违约等相关纠纷"},
        {"code": "labor", "name": "劳动纠纷", "description": "工资、加班、辞退等劳动关系纠纷"},
        {"code": "real_estate", "name": "房产纠纷", "description": "房屋买卖、租赁、物业等纠纷"},
        {"code": "traffic", "name": "交通事故", "description": "交通事故责任、赔偿等纠纷"},
        {"code": "debt", "name": "债务纠纷", "description": "借款、欠款、债权债务纠纷"},
        {"code": "family", "name": "婚姻家庭", "description": "离婚、抚养、财产分割等纠纷"},
        {"code": "consumer", "name": "消费纠纷", "description": "商品质量、服务等消费者权益纠纷"},
        {"code": "general", "name": "其他纠纷", "description": "其他类型的民事纠纷"}
    ]

    return {"dispute_types": dispute_types}
