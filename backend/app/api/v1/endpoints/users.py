"""
用户管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.exceptions import NotFoundException, ValidationException
from app.schemas.user import (
    UserResponse,
    UserUpdate,
    UserPasswordUpdate,
    UserProfileUpdate,
    UserProfileResponse,
    UserWithProfile,
    UserListResponse,
    UserStatistics,
    UserActivityLog,
    UserSessionList
)
from app.services.users import UserService
from app.dependencies.auth import (
    get_current_user,
    get_current_active_user,
    require_user_management_permission
)
from app.models.user import User, UserType, UserStatus
from app.core.logging import business_logger

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/me", response_model=UserWithProfile)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """获取当前用户详细信息"""
    
    return UserWithProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        phone=current_user.phone,
        user_type=current_user.user_type,
        status=current_user.status,
        email_verified=current_user.email_verified,
        phone_verified=current_user.phone_verified,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        last_login_at=current_user.last_login_at,
        profile=UserProfileResponse.from_orm(current_user.profile) if current_user.profile else None
    )


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新当前用户信息"""
    
    logger.info(f"更新用户信息: {current_user.email}")
    
    user_service = UserService(db)
    updated_user = await user_service.update_user(str(current_user.id), user_update)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="update_profile",
        details=user_update.dict(exclude_unset=True)
    )
    
    return UserResponse.from_orm(updated_user)


@router.put("/me/password")
async def update_current_user_password(
    password_update: UserPasswordUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新当前用户密码"""
    
    logger.info(f"更新用户密码: {current_user.email}")
    
    user_service = UserService(db)
    success = await user_service.update_user_password(
        str(current_user.id),
        password_update.current_password,
        password_update.new_password
    )
    
    if not success:
        raise ValidationException("密码更新失败")
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="update_password"
    )
    
    return {"message": "密码更新成功"}


@router.put("/me/profile", response_model=UserProfileResponse)
async def update_current_user_profile(
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新当前用户配置"""
    
    logger.info(f"更新用户配置: {current_user.email}")
    
    user_service = UserService(db)
    updated_profile = await user_service.update_user_profile(str(current_user.id), profile_update)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="update_profile_settings",
        details=profile_update.dict(exclude_unset=True)
    )
    
    return UserProfileResponse.from_orm(updated_profile)


@router.get("/me/sessions", response_model=UserSessionList)
async def get_current_user_sessions(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取当前用户会话列表"""
    
    # TODO: 实现获取用户会话列表的逻辑
    return UserSessionList(sessions=[], total=0)


@router.delete("/me/sessions/{session_id}")
async def revoke_user_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """撤销用户会话"""
    
    logger.info(f"撤销用户会话: {current_user.email}, session_id: {session_id}")
    
    # TODO: 实现撤销用户会话的逻辑
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="revoke_session",
        details={"session_id": session_id}
    )
    
    return {"message": "会话已撤销"}


@router.delete("/me/sessions")
async def revoke_all_user_sessions(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """撤销所有用户会话"""
    
    logger.info(f"撤销所有用户会话: {current_user.email}")
    
    user_service = UserService(db)
    await user_service.deactivate_user_sessions(current_user.id)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="revoke_all_sessions"
    )
    
    return {"message": "所有会话已撤销"}


# 管理员功能
@router.get("/", response_model=UserListResponse)
async def get_users_list(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    user_type: Optional[UserType] = Query(None, description="用户类型过滤"),
    status: Optional[UserStatus] = Query(None, description="用户状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(require_user_management_permission),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取用户列表（管理员功能）"""
    
    logger.info(f"获取用户列表: skip={skip}, limit={limit}")
    
    user_service = UserService(db)
    users, total = await user_service.get_users_list(
        skip=skip,
        limit=limit,
        user_type=user_type,
        status=status,
        search=search
    )
    
    pages = (total + limit - 1) // limit
    
    return UserListResponse(
        users=[UserResponse.from_orm(user) for user in users],
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=pages
    )


@router.get("/{user_id}", response_model=UserWithProfile)
async def get_user_by_id(
    user_id: str,
    current_user: User = Depends(require_user_management_permission),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """根据ID获取用户详细信息（管理员功能）"""
    
    logger.info(f"获取用户详细信息: {user_id}")
    
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    
    if not user:
        raise NotFoundException("用户不存在")
    
    return UserWithProfile(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        phone=user.phone,
        user_type=user.user_type,
        status=user.status,
        email_verified=user.email_verified,
        phone_verified=user.phone_verified,
        created_at=user.created_at,
        updated_at=user.updated_at,
        last_login_at=user.last_login_at,
        profile=UserProfileResponse.from_orm(user.profile) if user.profile else None
    )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user_by_id(
    user_id: str,
    user_update: UserUpdate,
    current_user: User = Depends(require_user_management_permission),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """更新用户信息（管理员功能）"""
    
    logger.info(f"管理员更新用户信息: {user_id}")
    
    user_service = UserService(db)
    updated_user = await user_service.update_user(user_id, user_update)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="admin_update_user",
        resource_type="user",
        resource_id=user_id,
        details=user_update.dict(exclude_unset=True)
    )
    
    return UserResponse.from_orm(updated_user)


@router.delete("/{user_id}")
async def delete_user_by_id(
    user_id: str,
    current_user: User = Depends(require_user_management_permission),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """删除用户（管理员功能）"""
    
    logger.info(f"管理员删除用户: {user_id}")
    
    # 不能删除自己
    if str(current_user.id) == user_id:
        raise ValidationException("不能删除自己的账户")
    
    user_service = UserService(db)
    success = await user_service.delete_user(user_id)
    
    if not success:
        raise NotFoundException("用户不存在")
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="admin_delete_user",
        resource_type="user",
        resource_id=user_id
    )
    
    return {"message": "用户删除成功"}


@router.get("/statistics/overview", response_model=UserStatistics)
async def get_user_statistics(
    current_user: User = Depends(require_user_management_permission),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取用户统计信息（管理员功能）"""
    
    logger.info("获取用户统计信息")
    
    # TODO: 实现用户统计逻辑
    return UserStatistics(
        total_users=0,
        active_users=0,
        new_users_today=0,
        new_users_this_week=0,
        new_users_this_month=0,
        user_type_distribution={},
        status_distribution={}
    )
