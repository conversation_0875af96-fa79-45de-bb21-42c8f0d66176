"""
历史记录API端点
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.favorites import HistoryService
from app.schemas.history import HistoryResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=HistoryResponse)
async def get_user_history(
    history_type: Optional[str] = Query(None, description="历史记录类型: qa, contract, document"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取用户历史记录"""
    
    history_service = HistoryService(db)
    
    if history_type == "qa":
        records, total = await history_service.get_user_qa_history(
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
    elif history_type == "contract":
        records, total = await history_service.get_user_contract_history(
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
    elif history_type == "document":
        records, total = await history_service.get_user_document_history(
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
    else:
        records, total = await history_service.get_user_all_history(
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
    
    return HistoryResponse(
        records=records,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/qa", response_model=HistoryResponse)
async def get_qa_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取问答历史"""
    
    history_service = HistoryService(db)
    records, total = await history_service.get_user_qa_history(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    
    return HistoryResponse(
        records=records,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/contracts", response_model=HistoryResponse)
async def get_contract_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同分析历史"""
    
    history_service = HistoryService(db)
    records, total = await history_service.get_user_contract_history(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    
    return HistoryResponse(
        records=records,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/documents", response_model=HistoryResponse)
async def get_document_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取文书生成历史"""
    
    history_service = HistoryService(db)
    records, total = await history_service.get_user_document_history(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    
    return HistoryResponse(
        records=records,
        total=total,
        page=skip // limit + 1,
        size=limit
    )
