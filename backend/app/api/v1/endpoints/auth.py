"""
认证相关API端点
"""

from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.config import settings
from app.core.database import get_db
from app.core.security import (
    create_access_token, 
    create_refresh_token,
    verify_refresh_token,
    verify_password,
    get_password_hash,
    PasswordValidator,
    create_email_verification_token,
    verify_email_verification_token,
    token_blacklist
)
from app.core.exceptions import (
    AuthenticationException,
    ValidationException,
    NotFoundException,
    ConflictException
)
from app.schemas.auth import (
    Token,
    UserLogin,
    UserRegister,
    UserResponse,
    RefreshTokenRequest,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest
)
from app.schemas.user import UserCreate
from app.services.users import UserService
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.core.logging import business_logger

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """用户注册"""
    
    logger.info(f"用户注册请求: {user_data.email}")
    
    # 验证密码强度
    is_strong, errors = PasswordValidator.validate_password_strength(user_data.password)
    if not is_strong:
        raise ValidationException("密码强度不足", detail={"errors": errors})
    
    # 检查用户是否已存在
    user_service = UserService(db)
    
    existing_user = await user_service.get_user_by_email(user_data.email)
    if existing_user:
        raise ConflictException("邮箱已被注册")
    
    existing_user = await user_service.get_user_by_username(user_data.username)
    if existing_user:
        raise ConflictException("用户名已被使用")
    
    # 创建用户
    user_create = UserCreate(
        username=user_data.username,
        email=user_data.email,
        password=user_data.password,
        full_name=user_data.full_name,
        phone=user_data.phone,
        user_type=user_data.user_type
    )
    
    user = await user_service.create_user(user_create)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(user.id),
        action="register",
        details={
            "email": user.email,
            "user_type": user.user_type,
            "ip_address": str(request.client.host) if request.client else None
        }
    )
    
    logger.info(f"用户注册成功: {user.email}")
    
    return UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        user_type=user.user_type,
        status=user.status,
        email_verified=user.email_verified,
        created_at=user.created_at
    )


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """用户登录"""
    
    logger.info(f"用户登录请求: {form_data.username}")
    
    user_service = UserService(db)
    
    # 验证用户凭据
    user = await user_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        business_logger.log_user_action(
            user_id="unknown",
            action="login_failed",
            details={
                "username": form_data.username,
                "ip_address": str(request.client.host) if request and request.client else None
            }
        )
        raise AuthenticationException("用户名或密码错误")
    
    # 创建访问令牌和刷新令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id),
        expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(subject=str(user.id))
    
    # 创建用户会话
    await user_service.create_user_session(
        user_id=user.id,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=str(request.client.host) if request and request.client else None,
        user_agent=request.headers.get("user-agent") if request else None
    )
    
    # 更新最后登录时间
    await user_service.update_last_login(user.id)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(user.id),
        action="login",
        details={
            "ip_address": str(request.client.host) if request and request.client else None
        }
    )
    
    logger.info(f"用户登录成功: {user.email}")
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/login/json", response_model=Token)
async def login_json(
    user_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """JSON格式用户登录"""
    
    logger.info(f"JSON登录请求: {user_data.username}")
    
    user_service = UserService(db)
    
    # 验证用户凭据
    user = await user_service.authenticate_user(user_data.username, user_data.password)
    if not user:
        business_logger.log_user_action(
            user_id="unknown",
            action="login_failed",
            details={
                "username": user_data.username,
                "ip_address": str(request.client.host) if request.client else None
            }
        )
        raise AuthenticationException("用户名或密码错误")
    
    # 创建访问令牌和刷新令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id),
        expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(subject=str(user.id))
    
    # 创建用户会话
    await user_service.create_user_session(
        user_id=user.id,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=str(request.client.host) if request.client else None,
        user_agent=request.headers.get("user-agent")
    )
    
    # 更新最后登录时间
    await user_service.update_last_login(user.id)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(user.id),
        action="login",
        details={
            "ip_address": str(request.client.host) if request.client else None
        }
    )
    
    logger.info(f"用户登录成功: {user.email}")
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """刷新访问令牌"""
    
    logger.info("刷新令牌请求")
    
    # 验证刷新令牌
    user_id = verify_refresh_token(refresh_data.refresh_token)
    if not user_id:
        raise AuthenticationException("无效的刷新令牌")
    
    # 检查用户是否存在
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise AuthenticationException("用户不存在")
    
    # 验证会话是否存在
    session = await user_service.get_user_session_by_refresh_token(refresh_data.refresh_token)
    if not session or not session.is_active:
        raise AuthenticationException("会话已失效")
    
    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id),
        expires_delta=access_token_expires
    )
    
    # 更新会话
    await user_service.update_user_session_token(session.id, access_token)
    
    logger.info(f"令牌刷新成功: {user.email}")
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_data.refresh_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """用户登出"""
    
    logger.info(f"用户登出请求: {current_user.email}")
    
    user_service = UserService(db)
    
    # 使所有用户会话失效
    await user_service.deactivate_user_sessions(current_user.id)
    
    # 记录业务日志
    business_logger.log_user_action(
        user_id=str(current_user.id),
        action="logout"
    )
    
    logger.info(f"用户登出成功: {current_user.email}")
    
    return {"message": "登出成功"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
) -> Any:
    """获取当前用户信息"""
    
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        phone=current_user.phone,
        user_type=current_user.user_type,
        status=current_user.status,
        email_verified=current_user.email_verified,
        phone_verified=current_user.phone_verified,
        created_at=current_user.created_at,
        last_login_at=current_user.last_login_at
    )


@router.post("/verify-email")
async def send_email_verification(
    request_data: EmailVerificationRequest,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """发送邮箱验证"""
    
    logger.info(f"发送邮箱验证请求: {request_data.email}")
    
    user_service = UserService(db)
    user = await user_service.get_user_by_email(request_data.email)
    
    if not user:
        raise NotFoundException("用户不存在")
    
    if user.email_verified:
        return {"message": "邮箱已验证"}
    
    # 创建验证令牌
    verification_token = create_email_verification_token(user.email)
    
    # TODO: 发送验证邮件
    # await send_verification_email(user.email, verification_token)
    
    logger.info(f"邮箱验证邮件已发送: {user.email}")
    
    return {"message": "验证邮件已发送"}


@router.post("/confirm-email")
async def confirm_email_verification(
    token: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """确认邮箱验证"""
    
    logger.info("邮箱验证确认请求")
    
    # 验证令牌
    email = verify_email_verification_token(token)
    if not email:
        raise AuthenticationException("无效的验证令牌")
    
    # 更新用户邮箱验证状态
    user_service = UserService(db)
    user = await user_service.get_user_by_email(email)
    
    if not user:
        raise NotFoundException("用户不存在")
    
    await user_service.verify_user_email(user.id)
    
    logger.info(f"邮箱验证成功: {email}")
    
    return {"message": "邮箱验证成功"}
