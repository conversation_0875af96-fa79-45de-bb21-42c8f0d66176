"""
合同工具API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user, check_contract_rate_limit
from app.models.user import User
from app.services.contracts import ContractAnalysisService
from app.schemas.contracts import (
    ContractAnalysisResponse,
    ContractTemplatesResponse,
    ContractReviewsResponse
)
from app.core.exceptions import ValidationException

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/templates", response_model=ContractTemplatesResponse)
async def get_contract_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同模板列表"""

    contract_service = ContractAnalysisService(db)
    templates, total = await contract_service.get_contract_templates(
        skip=skip,
        limit=limit,
        category=category
    )

    return ContractTemplatesResponse(
        templates=templates,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.post("/analyze", response_model=ContractAnalysisResponse)
async def analyze_contract(
    file: UploadFile = File(...),
    contract_name: str = Form(...),
    current_user: User = Depends(check_contract_rate_limit),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """分析合同风险"""

    logger.info(f"用户上传合同分析: {current_user.username}, file: {file.filename}")

    # 验证文件
    if not file.filename:
        raise ValidationException("请选择要分析的合同文件")

    # 获取文件扩展名
    import os
    file_ext = os.path.splitext(file.filename)[1].lower()

    # 读取文件内容
    file_content = await file.read()

    contract_service = ContractAnalysisService(db)
    contract_review = await contract_service.analyze_contract(
        user_id=current_user.id,
        contract_name=contract_name,
        file_content=file_content,
        file_type=file_ext
    )

    return ContractAnalysisResponse.from_orm(contract_review)


@router.get("/reviews", response_model=ContractReviewsResponse)
async def get_contract_reviews(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同审查记录"""

    contract_service = ContractAnalysisService(db)
    reviews, total = await contract_service.get_contract_reviews(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )

    from app.schemas.contracts import ContractReviewSummary

    return ContractReviewsResponse(
        reviews=[ContractReviewSummary.from_orm(review) for review in reviews],
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/reviews/{review_id}", response_model=ContractAnalysisResponse)
async def get_contract_review_detail(
    review_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同审查详情"""

    from uuid import UUID
    from app.core.exceptions import NotFoundException

    try:
        review_uuid = UUID(review_id)
    except ValueError:
        raise NotFoundException("合同审查记录不存在")

    contract_service = ContractAnalysisService(db)
    review = await contract_service.get_contract_review(review_uuid, current_user.id)

    if not review:
        raise NotFoundException("合同审查记录不存在")

    return ContractAnalysisResponse.from_orm(review)


@router.get("/templates/{template_id}")
async def get_contract_template_detail(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取合同模板详情"""

    # TODO: 实现合同模板详情查询
    return {"template": None}


@router.post("/templates/{template_id}/generate")
async def generate_contract_from_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """基于模板生成合同"""

    # TODO: 实现基于模板生成合同
    return {"contract_id": "contract_123", "download_url": "/contracts/download/contract_123"}
