"""
案例检索API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.cases import CaseSearchService
from app.schemas.cases import CaseSearchResponse, CaseDetailResponse, CaseStatisticsResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/search", response_model=CaseSearchResponse)
async def search_cases(
    q: str = Query(..., description="搜索关键词"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    case_type: Optional[str] = Query(None, description="案件类型"),
    court: Optional[str] = Query(None, description="审理法院"),
    date_from: Optional[str] = Query(None, description="判决日期起始"),
    date_to: Optional[str] = Query(None, description="判决日期结束"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """搜索案例"""

    logger.info(f"用户搜索案例: {current_user.username}, query: {q}")

    case_service = CaseSearchService(db)
    cases, total = await case_service.search_cases(
        query=q,
        skip=skip,
        limit=limit,
        case_type=case_type,
        court=court,
        date_from=date_from,
        date_to=date_to,
        user_id=current_user.id
    )

    return CaseSearchResponse(
        cases=cases,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{case_id}", response_model=CaseDetailResponse)
async def get_case_detail(
    case_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取案例详情"""

    from app.core.exceptions import NotFoundException

    case_service = CaseSearchService(db)
    case_data = await case_service.get_case_detail(case_id, current_user.id)

    if not case_data:
        raise NotFoundException("案例不存在")

    return CaseDetailResponse(**case_data)


@router.get("/{case_id}/similar")
async def get_similar_cases(
    case_id: str,
    limit: int = Query(5, ge=1, le=20),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取相似案例"""

    from app.schemas.cases import SimilarCaseResponse

    case_service = CaseSearchService(db)
    similar_cases = await case_service.get_similar_cases(
        case_id=case_id,
        limit=limit,
        user_id=current_user.id
    )

    return SimilarCaseResponse(cases=similar_cases)


@router.get("/statistics/overview")
async def get_case_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取案例统计信息"""

    case_service = CaseSearchService(db)
    stats = await case_service.get_case_statistics()

    return CaseStatisticsResponse(**stats)


@router.post("/{case_id}/favorite")
async def add_case_to_favorites(
    case_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """收藏案例"""

    # TODO: 实现案例收藏功能
    return {"message": "案例已收藏"}


@router.delete("/{case_id}/favorite")
async def remove_case_from_favorites(
    case_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """取消收藏案例"""

    # TODO: 实现取消收藏功能
    return {"message": "已取消收藏"}
