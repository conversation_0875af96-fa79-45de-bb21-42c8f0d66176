"""
收藏功能API端点
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.models.favorite import ItemType
from app.services.favorites import FavoriteService
from app.schemas.favorites import (
    FavoriteRequest,
    FavoriteResponse,
    FavoritesListResponse,
    FavoriteStatisticsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=FavoriteResponse)
async def add_favorite(
    request: FavoriteRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """添加收藏"""
    
    logger.info(f"用户添加收藏: {current_user.username}, type: {request.item_type}")
    
    favorite_service = FavoriteService(db)
    favorite = await favorite_service.add_favorite(
        user_id=current_user.id,
        item_type=request.item_type,
        item_id=request.item_id,
        item_title=request.item_title,
        item_data=request.item_data
    )
    
    return FavoriteResponse.from_orm(favorite)


@router.delete("/{item_type}/{item_id}")
async def remove_favorite(
    item_type: ItemType = Path(...),
    item_id: str = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """移除收藏"""
    
    logger.info(f"用户移除收藏: {current_user.username}, type: {item_type}, id: {item_id}")
    
    favorite_service = FavoriteService(db)
    await favorite_service.remove_favorite(
        user_id=current_user.id,
        item_type=item_type,
        item_id=item_id
    )
    
    return {"message": "收藏已移除"}


@router.get("/", response_model=FavoritesListResponse)
async def get_favorites(
    item_type: Optional[ItemType] = Query(None, description="收藏项类型"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取收藏列表"""
    
    favorite_service = FavoriteService(db)
    favorites, total = await favorite_service.get_user_favorites(
        user_id=current_user.id,
        item_type=item_type,
        skip=skip,
        limit=limit
    )
    
    return FavoritesListResponse(
        favorites=[FavoriteResponse.from_orm(fav) for fav in favorites],
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/check/{item_type}/{item_id}")
async def check_favorite_status(
    item_type: ItemType = Path(...),
    item_id: str = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """检查收藏状态"""
    
    favorite_service = FavoriteService(db)
    is_favorited = await favorite_service.is_favorited(
        user_id=current_user.id,
        item_type=item_type,
        item_id=item_id
    )
    
    return {"is_favorited": is_favorited}


@router.get("/statistics", response_model=FavoriteStatisticsResponse)
async def get_favorite_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """获取收藏统计"""
    
    favorite_service = FavoriteService(db)
    stats = await favorite_service.get_favorite_statistics(current_user.id)
    
    return FavoriteStatisticsResponse(**stats)
