"""
认证相关依赖注入
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.security import verify_token, token_blacklist
from app.core.exceptions import AuthenticationException, AuthorizationException
from app.models.user import User, UserStatus
from app.services.users import UserService

logger = logging.getLogger(__name__)

# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户"""
    
    # 检查是否提供了认证凭据
    if not credentials:
        logger.warning("未提供认证凭据")
        raise AuthenticationException("未提供认证凭据")
    
    # 获取令牌
    token = credentials.credentials
    
    # 检查令牌是否在黑名单中
    if token_blacklist.is_blacklisted(token):
        logger.warning("令牌已被列入黑名单")
        raise AuthenticationException("令牌已失效")
    
    # 验证令牌
    user_id = verify_token(token)
    if not user_id:
        logger.warning("令牌验证失败")
        raise AuthenticationException("无效的认证令牌")
    
    # 获取用户信息
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    
    if not user:
        logger.warning(f"用户不存在: {user_id}")
        raise AuthenticationException("用户不存在")
    
    # 检查用户状态
    if user.status != UserStatus.ACTIVE:
        logger.warning(f"用户状态异常: {user.status}")
        raise AuthenticationException("用户账户已被禁用")
    
    logger.info(f"用户认证成功: {user.username}")
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if current_user.status != UserStatus.ACTIVE:
        raise AuthenticationException("用户账户未激活")
    return current_user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """获取当前用户（可选）"""
    try:
        return await get_current_user(credentials, db)
    except AuthenticationException:
        return None


def require_user_type(*allowed_types: str):
    """要求特定用户类型的装饰器"""
    def decorator(current_user: User = Depends(get_current_user)):
        if current_user.user_type not in allowed_types:
            logger.warning(f"用户类型权限不足: {current_user.user_type}, 需要: {allowed_types}")
            raise AuthorizationException(f"需要{'/'.join(allowed_types)}用户权限")
        return current_user
    return decorator


def require_lawyer_user():
    """要求律师用户权限"""
    return require_user_type("lawyer")


def require_enterprise_user():
    """要求企业用户权限"""
    return require_user_type("enterprise")


def require_individual_user():
    """要求个人用户权限"""
    return require_user_type("individual")


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, max_requests: int, time_window: int):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}
    
    def is_allowed(self, user_id: str) -> bool:
        """检查是否允许请求"""
        import time
        
        current_time = time.time()
        
        # 清理过期记录
        if user_id in self.requests:
            self.requests[user_id] = [
                req_time for req_time in self.requests[user_id]
                if current_time - req_time < self.time_window
            ]
        else:
            self.requests[user_id] = []
        
        # 检查请求数量
        if len(self.requests[user_id]) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests[user_id].append(current_time)
        return True


# 创建不同的频率限制器
qa_rate_limiter = RateLimiter(max_requests=10, time_window=60)  # 每分钟10次问答
contract_rate_limiter = RateLimiter(max_requests=5, time_window=300)  # 每5分钟5次合同分析
document_rate_limiter = RateLimiter(max_requests=3, time_window=300)  # 每5分钟3次文书生成


def check_qa_rate_limit(current_user: User = Depends(get_current_user)):
    """检查问答频率限制"""
    if not qa_rate_limiter.is_allowed(str(current_user.id)):
        logger.warning(f"用户问答请求过于频繁: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="问答请求过于频繁，请稍后再试"
        )
    return current_user


def check_contract_rate_limit(current_user: User = Depends(get_current_user)):
    """检查合同分析频率限制"""
    if not contract_rate_limiter.is_allowed(str(current_user.id)):
        logger.warning(f"用户合同分析请求过于频繁: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="合同分析请求过于频繁，请稍后再试"
        )
    return current_user


def check_document_rate_limit(current_user: User = Depends(get_current_user)):
    """检查文书生成频率限制"""
    if not document_rate_limiter.is_allowed(str(current_user.id)):
        logger.warning(f"用户文书生成请求过于频繁: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="文书生成请求过于频繁，请稍后再试"
        )
    return current_user


async def verify_user_ownership(
    resource_user_id: str,
    current_user: User = Depends(get_current_user)
) -> User:
    """验证用户是否拥有资源"""
    if str(current_user.id) != resource_user_id:
        logger.warning(f"用户尝试访问他人资源: {current_user.username}")
        raise AuthorizationException("无权访问此资源")
    return current_user


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def can_access_admin_panel(user: User) -> bool:
        """检查是否可以访问管理面板"""
        # 这里可以根据实际需求实现权限逻辑
        return user.user_type == "lawyer" and user.status == UserStatus.ACTIVE
    
    @staticmethod
    def can_manage_users(user: User) -> bool:
        """检查是否可以管理用户"""
        # 这里可以根据实际需求实现权限逻辑
        return user.user_type == "lawyer"
    
    @staticmethod
    def can_access_advanced_features(user: User) -> bool:
        """检查是否可以访问高级功能"""
        return user.user_type in ["lawyer", "enterprise"]


def require_admin_permission(current_user: User = Depends(get_current_user)):
    """要求管理员权限"""
    if not PermissionChecker.can_access_admin_panel(current_user):
        raise AuthorizationException("需要管理员权限")
    return current_user


def require_user_management_permission(current_user: User = Depends(get_current_user)):
    """要求用户管理权限"""
    if not PermissionChecker.can_manage_users(current_user):
        raise AuthorizationException("需要用户管理权限")
    return current_user
