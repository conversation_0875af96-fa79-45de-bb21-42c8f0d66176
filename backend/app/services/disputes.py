"""
纠纷解决服务
"""

import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.exceptions import ValidationException
from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class DisputeResolutionService:
    """纠纷解决服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def analyze_dispute(
        self,
        user_id: uuid.UUID,
        dispute_description: str,
        dispute_type: Optional[str] = None,
        amount_involved: Optional[float] = None,
        parties_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """分析纠纷情况"""
        
        logger.info(f"分析纠纷: user_id={user_id}, type={dispute_type}")
        
        # 验证输入
        if not dispute_description or len(dispute_description.strip()) < 10:
            raise ValidationException("请详细描述纠纷情况，至少10个字符")
        
        if len(dispute_description) > 5000:
            raise ValidationException("纠纷描述过长，请控制在5000字以内")
        
        # 自动检测纠纷类型（如果未提供）
        if not dispute_type:
            dispute_type = await self._detect_dispute_type(dispute_description)
        
        # 分析纠纷严重程度
        severity = await self._analyze_dispute_severity(
            dispute_description, amount_involved
        )
        
        # 生成解决建议
        suggestions = await self._generate_resolution_suggestions(
            dispute_type, dispute_description, amount_involved, severity
        )
        
        # 获取相关法律依据
        legal_basis = await self._get_legal_basis(dispute_type)
        
        # 估算解决时间和成本
        time_cost_estimate = await self._estimate_time_and_cost(
            dispute_type, severity, amount_involved
        )
        
        result = {
            "dispute_type": dispute_type,
            "severity": severity,
            "suggestions": suggestions,
            "legal_basis": legal_basis,
            "time_estimate": time_cost_estimate["time"],
            "cost_estimate": time_cost_estimate["cost"],
            "success_rate": time_cost_estimate["success_rate"]
        }
        
        # 记录业务日志
        business_logger.log_user_action(
            user_id=str(user_id),
            action="analyze_dispute",
            details={
                "dispute_type": dispute_type,
                "severity": severity,
                "description_length": len(dispute_description)
            }
        )
        
        return result
    
    async def _detect_dispute_type(self, description: str) -> str:
        """自动检测纠纷类型"""
        
        description_lower = description.lower()
        
        # 合同纠纷
        if any(keyword in description_lower for keyword in ["合同", "协议", "违约", "履行"]):
            return "contract"
        
        # 劳动纠纷
        elif any(keyword in description_lower for keyword in ["工资", "加班", "辞退", "劳动", "社保"]):
            return "labor"
        
        # 房产纠纷
        elif any(keyword in description_lower for keyword in ["房屋", "租赁", "买卖", "物业", "装修"]):
            return "real_estate"
        
        # 交通事故
        elif any(keyword in description_lower for keyword in ["交通", "车祸", "撞车", "事故", "保险"]):
            return "traffic"
        
        # 债务纠纷
        elif any(keyword in description_lower for keyword in ["借款", "欠款", "债务", "还钱", "利息"]):
            return "debt"
        
        # 婚姻家庭
        elif any(keyword in description_lower for keyword in ["离婚", "抚养", "财产分割", "赡养"]):
            return "family"
        
        # 消费纠纷
        elif any(keyword in description_lower for keyword in ["购买", "商品", "服务", "退款", "质量"]):
            return "consumer"
        
        else:
            return "general"
    
    async def _analyze_dispute_severity(
        self, 
        description: str, 
        amount_involved: Optional[float] = None
    ) -> str:
        """分析纠纷严重程度"""
        
        severity_score = 0
        
        # 基于金额判断
        if amount_involved:
            if amount_involved >= 100000:  # 10万以上
                severity_score += 3
            elif amount_involved >= 50000:  # 5万以上
                severity_score += 2
            elif amount_involved >= 10000:  # 1万以上
                severity_score += 1
        
        # 基于描述内容判断
        high_severity_keywords = ["诈骗", "威胁", "暴力", "人身伤害", "重大损失"]
        medium_severity_keywords = ["违约", "拖欠", "纠纷", "争议", "不履行"]
        
        description_lower = description.lower()
        
        for keyword in high_severity_keywords:
            if keyword in description_lower:
                severity_score += 2
                break
        
        for keyword in medium_severity_keywords:
            if keyword in description_lower:
                severity_score += 1
                break
        
        # 确定严重程度
        if severity_score >= 4:
            return "high"
        elif severity_score >= 2:
            return "medium"
        else:
            return "low"
    
    async def _generate_resolution_suggestions(
        self,
        dispute_type: str,
        description: str,
        amount_involved: Optional[float] = None,
        severity: str = "medium"
    ) -> List[Dict[str, Any]]:
        """生成解决建议"""
        
        suggestions = []
        
        # 通用建议
        suggestions.append({
            "method": "negotiation",
            "title": "协商解决",
            "description": "首先尝试与对方进行友好协商，这是最经济、最快速的解决方式",
            "pros": ["成本低", "时间短", "关系维护"],
            "cons": ["可能无法达成一致"],
            "success_rate": 0.6,
            "time_estimate": "1-2周",
            "cost_estimate": "几乎无成本"
        })
        
        # 基于纠纷类型的特定建议
        if dispute_type == "contract":
            suggestions.extend([
                {
                    "method": "mediation",
                    "title": "调解",
                    "description": "通过第三方调解机构进行调解，具有法律效力",
                    "pros": ["有法律效力", "成本适中", "保密性好"],
                    "cons": ["需要双方同意"],
                    "success_rate": 0.7,
                    "time_estimate": "1-3个月",
                    "cost_estimate": "1000-5000元"
                },
                {
                    "method": "arbitration",
                    "title": "仲裁",
                    "description": "如果合同中有仲裁条款，可以申请仲裁",
                    "pros": ["程序简便", "专业性强", "一裁终局"],
                    "cons": ["费用较高", "不能上诉"],
                    "success_rate": 0.8,
                    "time_estimate": "3-6个月",
                    "cost_estimate": "5000-20000元"
                }
            ])
        
        elif dispute_type == "labor":
            suggestions.extend([
                {
                    "method": "labor_arbitration",
                    "title": "劳动仲裁",
                    "description": "向劳动仲裁委员会申请仲裁，这是劳动纠纷的必经程序",
                    "pros": ["免费", "专业", "程序规范"],
                    "cons": ["时间较长"],
                    "success_rate": 0.75,
                    "time_estimate": "2-4个月",
                    "cost_estimate": "免费"
                }
            ])
        
        # 严重纠纷建议诉讼
        if severity == "high" or (amount_involved and amount_involved >= 50000):
            suggestions.append({
                "method": "litigation",
                "title": "诉讼",
                "description": "向人民法院提起诉讼，通过司法途径解决",
                "pros": ["权威性强", "强制执行", "法律保障"],
                "cons": ["费用高", "时间长", "程序复杂"],
                "success_rate": 0.65,
                "time_estimate": "6个月-2年",
                "cost_estimate": "5000-50000元"
            })
        
        return suggestions
    
    async def _get_legal_basis(self, dispute_type: str) -> List[str]:
        """获取相关法律依据"""
        
        legal_basis_map = {
            "contract": [
                "《中华人民共和国民法典》合同编",
                "《中华人民共和国合同法》（已废止，相关条款并入民法典）",
                "相关司法解释"
            ],
            "labor": [
                "《中华人民共和国劳动法》",
                "《中华人民共和国劳动合同法》",
                "《劳动争议调解仲裁法》"
            ],
            "real_estate": [
                "《中华人民共和国民法典》物权编",
                "《城市房地产管理法》",
                "相关地方性法规"
            ],
            "traffic": [
                "《中华人民共和国道路交通安全法》",
                "《机动车交通事故责任强制保险条例》",
                "相关司法解释"
            ],
            "debt": [
                "《中华人民共和国民法典》债权债务相关条款",
                "《最高人民法院关于审理民间借贷案件适用法律若干问题的规定》"
            ],
            "family": [
                "《中华人民共和国民法典》婚姻家庭编",
                "《中华人民共和国婚姻法》（已废止，相关条款并入民法典）"
            ],
            "consumer": [
                "《中华人民共和国消费者权益保护法》",
                "《产品质量法》",
                "相关行政法规"
            ]
        }
        
        return legal_basis_map.get(dispute_type, ["《中华人民共和国民法典》", "相关法律法规"])
    
    async def _estimate_time_and_cost(
        self,
        dispute_type: str,
        severity: str,
        amount_involved: Optional[float] = None
    ) -> Dict[str, Any]:
        """估算解决时间和成本"""
        
        base_time = {
            "low": "1-3个月",
            "medium": "3-6个月", 
            "high": "6个月-1年"
        }
        
        base_cost = {
            "low": "1000-5000元",
            "medium": "5000-20000元",
            "high": "20000-50000元"
        }
        
        success_rate = {
            "low": 0.8,
            "medium": 0.7,
            "high": 0.6
        }
        
        return {
            "time": base_time.get(severity, "3-6个月"),
            "cost": base_cost.get(severity, "5000-20000元"),
            "success_rate": success_rate.get(severity, 0.7)
        }
    
    async def get_dispute_solutions(
        self,
        dispute_type: str,
        user_id: Optional[uuid.UUID] = None
    ) -> List[Dict[str, Any]]:
        """获取特定类型纠纷的解决方案"""
        
        # 记录查询日志
        if user_id:
            business_logger.log_user_action(
                user_id=str(user_id),
                action="get_dispute_solutions",
                details={"dispute_type": dispute_type}
            )
        
        # 返回该类型纠纷的常见解决方案
        return await self._generate_resolution_suggestions(dispute_type, "", None, "medium")
