"""
法律案例检索服务
"""

import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_elasticsearch
from app.core.exceptions import ExternalServiceException, NotFoundException
from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class CaseSearchService:
    """法律案例搜索服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def search_cases(
        self,
        query: str,
        skip: int = 0,
        limit: int = 20,
        case_type: Optional[str] = None,
        court: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        user_id: Optional[uuid.UUID] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """搜索法律案例"""
        
        logger.info(f"搜索法律案例: query={query}, limit={limit}")
        
        try:
            es = await get_elasticsearch()
            
            # 构建搜索查询
            search_body = self._build_search_query(
                query=query,
                case_type=case_type,
                court=court,
                date_from=date_from,
                date_to=date_to
            )
            
            # 执行搜索
            response = await es.search(
                index="legal_cases",
                body=search_body,
                from_=skip,
                size=limit
            )
            
            # 解析结果
            cases = []
            for hit in response["hits"]["hits"]:
                case_data = hit["_source"]
                case_data["id"] = hit["_id"]
                case_data["score"] = hit["_score"]
                cases.append(case_data)
            
            total = response["hits"]["total"]["value"]
            
            # 记录搜索日志
            if user_id:
                business_logger.log_user_action(
                    user_id=str(user_id),
                    action="search_cases",
                    details={
                        "query": query,
                        "results_count": len(cases),
                        "total_hits": total
                    }
                )
            
            logger.info(f"案例搜索完成: 找到{total}个结果")
            return cases, total
            
        except Exception as e:
            logger.error(f"案例搜索失败: {e}")
            raise ExternalServiceException("搜索服务暂时不可用")
    
    def _build_search_query(
        self,
        query: str,
        case_type: Optional[str] = None,
        court: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> Dict[str, Any]:
        """构建Elasticsearch搜索查询"""
        
        # 基础查询
        must_queries = []
        
        # 主要搜索查询
        if query:
            must_queries.append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "title^3",
                        "case_summary^2",
                        "facts^1.5",
                        "court_opinion^1.5",
                        "full_text^1"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        
        # 过滤条件
        filter_queries = []
        
        if case_type:
            filter_queries.append({
                "term": {"case_type.keyword": case_type}
            })
        
        if court:
            filter_queries.append({
                "term": {"court.keyword": court}
            })
        
        if date_from or date_to:
            date_range = {}
            if date_from:
                date_range["gte"] = date_from
            if date_to:
                date_range["lte"] = date_to
            
            filter_queries.append({
                "range": {"judgment_date": date_range}
            })
        
        # 构建完整查询
        search_body = {
            "query": {
                "bool": {
                    "must": must_queries,
                    "filter": filter_queries
                }
            },
            "highlight": {
                "fields": {
                    "title": {},
                    "case_summary": {},
                    "facts": {},
                    "court_opinion": {}
                },
                "pre_tags": ["<mark>"],
                "post_tags": ["</mark>"]
            },
            "sort": [
                {"_score": {"order": "desc"}},
                {"judgment_date": {"order": "desc"}}
            ]
        }
        
        return search_body
    
    async def get_case_detail(self, case_id: str, user_id: Optional[uuid.UUID] = None) -> Optional[Dict[str, Any]]:
        """获取案例详情"""
        
        logger.info(f"获取案例详情: case_id={case_id}")
        
        try:
            es = await get_elasticsearch()
            
            response = await es.get(
                index="legal_cases",
                id=case_id
            )
            
            case_data = response["_source"]
            case_data["id"] = response["_id"]
            
            # 记录查看日志
            if user_id:
                business_logger.log_user_action(
                    user_id=str(user_id),
                    action="view_case",
                    resource_type="case",
                    resource_id=case_id
                )
            
            return case_data
            
        except Exception as e:
            if "not_found" in str(e).lower():
                return None
            logger.error(f"获取案例详情失败: {e}")
            raise ExternalServiceException("案例服务暂时不可用")
    
    async def get_similar_cases(
        self,
        case_id: str,
        limit: int = 5,
        user_id: Optional[uuid.UUID] = None
    ) -> List[Dict[str, Any]]:
        """获取相似案例"""
        
        logger.info(f"获取相似案例: case_id={case_id}")
        
        try:
            es = await get_elasticsearch()
            
            # 首先获取原案例
            case_response = await es.get(
                index="legal_cases",
                id=case_id
            )
            
            case_data = case_response["_source"]
            
            # 使用More Like This查询找相似案例
            search_body = {
                "query": {
                    "more_like_this": {
                        "fields": ["title", "case_summary", "facts", "keywords"],
                        "like": [
                            {
                                "_index": "legal_cases",
                                "_id": case_id
                            }
                        ],
                        "min_term_freq": 1,
                        "max_query_terms": 12,
                        "min_doc_freq": 1
                    }
                },
                "size": limit
            }
            
            response = await es.search(
                index="legal_cases",
                body=search_body
            )
            
            similar_cases = []
            for hit in response["hits"]["hits"]:
                similar_case = hit["_source"]
                similar_case["id"] = hit["_id"]
                similar_case["similarity_score"] = hit["_score"]
                similar_cases.append(similar_case)
            
            # 记录查看日志
            if user_id:
                business_logger.log_user_action(
                    user_id=str(user_id),
                    action="view_similar_cases",
                    resource_type="case",
                    resource_id=case_id,
                    details={"similar_count": len(similar_cases)}
                )
            
            return similar_cases
            
        except Exception as e:
            logger.error(f"获取相似案例失败: {e}")
            return []
    
    async def get_case_statistics(self) -> Dict[str, Any]:
        """获取案例统计信息"""
        
        try:
            es = await get_elasticsearch()
            
            # 获取总数
            count_response = await es.count(index="legal_cases")
            total_cases = count_response["count"]
            
            # 按类型统计
            type_agg_response = await es.search(
                index="legal_cases",
                body={
                    "size": 0,
                    "aggs": {
                        "case_types": {
                            "terms": {
                                "field": "case_type.keyword",
                                "size": 10
                            }
                        }
                    }
                }
            )
            
            case_types = {}
            for bucket in type_agg_response["aggregations"]["case_types"]["buckets"]:
                case_types[bucket["key"]] = bucket["doc_count"]
            
            # 按法院统计
            court_agg_response = await es.search(
                index="legal_cases",
                body={
                    "size": 0,
                    "aggs": {
                        "courts": {
                            "terms": {
                                "field": "court.keyword",
                                "size": 10
                            }
                        }
                    }
                }
            )
            
            courts = {}
            for bucket in court_agg_response["aggregations"]["courts"]["buckets"]:
                courts[bucket["key"]] = bucket["doc_count"]
            
            return {
                "total_cases": total_cases,
                "case_types": case_types,
                "top_courts": courts
            }
            
        except Exception as e:
            logger.error(f"获取案例统计失败: {e}")
            return {
                "total_cases": 0,
                "case_types": {},
                "top_courts": {}
            }
