"""
自然语言处理服务
"""

import re
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Any, Optional, Tuple
from collections import Counter
import logging

logger = logging.getLogger(__name__)

# 加载自定义词典
jieba.load_userdict("app/data/legal_dict.txt")  # 法律专业词典


class LegalNLPProcessor:
    """法律领域NLP处理器"""
    
    def __init__(self):
        # 法律领域关键词
        self.legal_keywords = {
            "合同": ["合同", "协议", "约定", "条款", "违约", "履行", "解除", "终止"],
            "劳动": ["劳动", "工作", "雇佣", "工资", "加班", "辞职", "辞退", "社保"],
            "婚姻": ["婚姻", "离婚", "结婚", "财产", "抚养", "赡养", "分割"],
            "房产": ["房屋", "房产", "买卖", "租赁", "物业", "装修", "产权"],
            "交通": ["交通", "车祸", "事故", "撞车", "保险", "赔偿", "责任"],
            "刑事": ["犯罪", "刑事", "盗窃", "诈骗", "故意", "过失", "判决"],
            "民事": ["民事", "侵权", "损害", "赔偿", "诉讼", "仲裁", "调解"],
            "公司": ["公司", "企业", "股东", "董事", "经营", "破产", "清算"],
            "知识产权": ["专利", "商标", "著作权", "版权", "侵权", "许可"],
            "税务": ["税务", "纳税", "发票", "税收", "申报", "减免"]
        }
        
        # 停用词
        self.stop_words = self._load_stop_words()
        
        # 法律实体识别模式
        self.entity_patterns = {
            "法条": re.compile(r'《[^》]+》第?\d+条?'),
            "金额": re.compile(r'\d+(?:\.\d+)?(?:万|千|百)?元'),
            "日期": re.compile(r'\d{4}年\d{1,2}月\d{1,2}日'),
            "时间": re.compile(r'\d{1,2}(?:个)?(?:月|年|日|天)'),
            "法院": re.compile(r'[^，。！？]*(?:法院|仲裁委员会)'),
            "当事人": re.compile(r'(?:原告|被告|申请人|被申请人|甲方|乙方)')
        }
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "什么", "可以", "怎么", "如何", "为什么", "吗", "呢"
        }
        return stop_words
    
    def tokenize(self, text: str) -> List[str]:
        """分词"""
        # 清理文本
        text = self._clean_text(text)
        
        # 使用jieba分词
        tokens = jieba.lcut(text)
        
        # 过滤停用词和短词
        filtered_tokens = [
            token for token in tokens 
            if token not in self.stop_words and len(token) > 1
        ]
        
        return filtered_tokens
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[Tuple[str, float]]:
        """提取关键词"""
        import jieba.analyse
        
        # 使用TF-IDF提取关键词
        keywords = jieba.analyse.extract_tags(
            text, 
            topK=top_k, 
            withWeight=True,
            allowPOS=('n', 'nr', 'ns', 'nt', 'nz', 'v', 'vn', 'a')
        )
        
        return keywords
    
    def classify_question(self, question: str) -> Dict[str, Any]:
        """问题分类"""
        question_clean = self._clean_text(question)
        tokens = self.tokenize(question_clean)
        
        # 计算每个类别的匹配分数
        category_scores = {}
        for category, keywords in self.legal_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in question_clean:
                    score += 2  # 完全匹配得分更高
                for token in tokens:
                    if keyword in token or token in keyword:
                        score += 1
            category_scores[category] = score
        
        # 找出最高分的类别
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            confidence = category_scores[best_category] / sum(category_scores.values()) if sum(category_scores.values()) > 0 else 0
        else:
            best_category = "general"
            confidence = 0.5
        
        return {
            "category": best_category,
            "confidence": confidence,
            "scores": category_scores
        }
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取法律实体"""
        entities = {}
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[entity_type] = list(set(matches))  # 去重
        
        return entities
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """情感分析"""
        # 简单的基于词典的情感分析
        positive_words = ["满意", "好", "赞", "支持", "同意", "感谢", "帮助"]
        negative_words = ["不满", "差", "糟糕", "反对", "不同意", "投诉", "问题"]
        
        text_clean = self._clean_text(text)
        tokens = self.tokenize(text_clean)
        
        positive_count = sum(1 for token in tokens if token in positive_words)
        negative_count = sum(1 for token in tokens if token in negative_words)
        
        total_count = positive_count + negative_count
        if total_count == 0:
            sentiment = "neutral"
            confidence = 0.5
        elif positive_count > negative_count:
            sentiment = "positive"
            confidence = positive_count / total_count
        else:
            sentiment = "negative"
            confidence = negative_count / total_count
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_count": positive_count,
            "negative_count": negative_count
        }
    
    def extract_intent(self, question: str) -> Dict[str, Any]:
        """意图识别"""
        question_clean = self._clean_text(question).lower()
        
        # 定义意图模式
        intent_patterns = {
            "咨询": ["怎么", "如何", "什么", "为什么", "是否", "可以", "能否"],
            "求助": ["帮助", "帮忙", "救助", "支援", "协助"],
            "投诉": ["投诉", "举报", "控告", "起诉", "告发"],
            "查询": ["查询", "查找", "搜索", "寻找", "了解"],
            "申请": ["申请", "办理", "提交", "递交", "登记"],
            "咨询费用": ["费用", "价格", "收费", "多少钱", "成本"]
        }
        
        intent_scores = {}
        for intent, patterns in intent_patterns.items():
            score = sum(1 for pattern in patterns if pattern in question_clean)
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[best_intent] / sum(intent_scores.values())
        else:
            best_intent = "咨询"  # 默认意图
            confidence = 0.5
        
        return {
            "intent": best_intent,
            "confidence": confidence,
            "scores": intent_scores
        }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字和基本标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？；：""''（）【】\s]', '', text)
        
        return text.strip()
    
    def similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        tokens1 = set(self.tokenize(text1))
        tokens2 = set(self.tokenize(text2))
        
        if not tokens1 and not tokens2:
            return 1.0
        if not tokens1 or not tokens2:
            return 0.0
        
        # 计算Jaccard相似度
        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))
        
        return intersection / union if union > 0 else 0.0
    
    def summarize(self, text: str, max_sentences: int = 3) -> str:
        """文本摘要"""
        # 简单的基于句子重要性的摘要
        sentences = re.split(r'[。！？]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 5]
        
        if len(sentences) <= max_sentences:
            return text
        
        # 计算句子重要性（基于关键词密度）
        keywords = [kw[0] for kw in self.extract_keywords(text, top_k=10)]
        
        sentence_scores = []
        for sentence in sentences:
            score = sum(1 for keyword in keywords if keyword in sentence)
            sentence_scores.append((sentence, score))
        
        # 选择得分最高的句子
        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in sentence_scores[:max_sentences]]
        
        return '。'.join(top_sentences) + '。'


# 全局NLP处理器实例
nlp_processor = LegalNLPProcessor()


def process_legal_text(text: str) -> Dict[str, Any]:
    """处理法律文本的便捷函数"""
    return {
        "tokens": nlp_processor.tokenize(text),
        "keywords": nlp_processor.extract_keywords(text),
        "classification": nlp_processor.classify_question(text),
        "entities": nlp_processor.extract_entities(text),
        "sentiment": nlp_processor.analyze_sentiment(text),
        "intent": nlp_processor.extract_intent(text),
        "summary": nlp_processor.summarize(text)
    }
