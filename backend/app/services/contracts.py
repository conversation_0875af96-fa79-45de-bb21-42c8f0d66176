"""
合同分析服务
"""

import uuid
import os
from datetime import datetime
from typing import Optional, List, Dict, Any, BinaryIO
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
import logging

from app.core.exceptions import ValidationException, ExternalServiceException
from app.models.contract import ContractReview, RiskLevel, ContractStatus
from app.core.logging import business_logger
from app.core.config import settings

logger = logging.getLogger(__name__)


class ContractAnalysisService:
    """合同分析服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def analyze_contract(
        self,
        user_id: uuid.UUID,
        contract_name: str,
        file_content: bytes,
        file_type: str
    ) -> ContractReview:
        """分析合同内容"""
        
        logger.info(f"开始分析合同: user_id={user_id}, name={contract_name}")
        
        # 验证文件类型
        if file_type not in ['.pdf', '.doc', '.docx', '.txt']:
            raise ValidationException("不支持的文件类型")
        
        # 验证文件大小
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise ValidationException("文件大小超过限制")
        
        # 保存文件
        file_path = await self._save_contract_file(contract_name, file_content, file_type)
        
        # 创建合同审查记录
        contract_review = ContractReview(
            id=uuid.uuid4(),
            user_id=user_id,
            contract_name=contract_name,
            file_path=file_path,
            contract_type=await self._detect_contract_type(file_content),
            status=ContractStatus.PROCESSING
        )
        
        self.db.add(contract_review)
        await self.db.commit()
        await self.db.refresh(contract_review)
        
        try:
            # 提取合同文本
            contract_text = await self._extract_text_from_file(file_content, file_type)
            
            # 分析合同风险
            analysis_result = await self._analyze_contract_risks(contract_text)
            
            # 更新分析结果
            contract_review.risk_level = analysis_result["risk_level"]
            contract_review.risk_points = analysis_result["risk_points"]
            contract_review.suggestions = analysis_result["suggestions"]
            contract_review.status = ContractStatus.COMPLETED
            
            await self.db.commit()
            await self.db.refresh(contract_review)
            
            # 记录业务日志
            business_logger.log_user_action(
                user_id=str(user_id),
                action="analyze_contract",
                resource_type="contract",
                resource_id=str(contract_review.id),
                details={
                    "contract_name": contract_name,
                    "risk_level": contract_review.risk_level,
                    "risk_count": len(contract_review.risk_points)
                }
            )
            
            logger.info(f"合同分析完成: contract_id={contract_review.id}")
            
        except Exception as e:
            # 更新失败状态
            contract_review.status = ContractStatus.FAILED
            await self.db.commit()
            
            logger.error(f"合同分析失败: {e}")
            raise ExternalServiceException("合同分析服务暂时不可用")
        
        return contract_review
    
    async def _save_contract_file(self, contract_name: str, file_content: bytes, file_type: str) -> str:
        """保存合同文件"""
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{contract_name}{file_type}"
        
        # 确保上传目录存在
        upload_dir = os.path.join(settings.UPLOAD_DIR, "contracts")
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_dir, filename)
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        return file_path
    
    async def _detect_contract_type(self, file_content: bytes) -> str:
        """检测合同类型"""
        
        # 简单的合同类型检测逻辑
        # 在实际应用中，这里应该使用更复杂的NLP模型
        
        try:
            text = file_content.decode('utf-8', errors='ignore').lower()
        except:
            return "unknown"
        
        if "劳动合同" in text or "雇佣" in text:
            return "labor"
        elif "房屋买卖" in text or "购房" in text:
            return "real_estate"
        elif "租赁" in text or "出租" in text:
            return "lease"
        elif "服务合同" in text or "服务协议" in text:
            return "service"
        elif "采购" in text or "供应" in text:
            return "procurement"
        else:
            return "general"
    
    async def _extract_text_from_file(self, file_content: bytes, file_type: str) -> str:
        """从文件中提取文本"""
        
        if file_type == '.txt':
            return file_content.decode('utf-8', errors='ignore')
        
        elif file_type == '.pdf':
            # TODO: 实现PDF文本提取
            # 这里使用模拟实现
            return "PDF文本内容提取功能开发中"
        
        elif file_type in ['.doc', '.docx']:
            # TODO: 实现Word文档文本提取
            # 这里使用模拟实现
            return "Word文档文本内容提取功能开发中"
        
        else:
            raise ValidationException("不支持的文件类型")
    
    async def _analyze_contract_risks(self, contract_text: str) -> Dict[str, Any]:
        """分析合同风险"""
        
        # 模拟合同风险分析
        # 在实际应用中，这里应该使用专业的法律AI模型
        
        risk_points = []
        suggestions = []
        risk_score = 0
        
        # 检查常见风险点
        if "违约金" not in contract_text:
            risk_points.append({
                "type": "missing_clause",
                "description": "缺少违约金条款",
                "severity": "medium",
                "location": "全文"
            })
            suggestions.append("建议添加明确的违约金条款")
            risk_score += 2
        
        if "争议解决" not in contract_text and "仲裁" not in contract_text:
            risk_points.append({
                "type": "missing_clause",
                "description": "缺少争议解决条款",
                "severity": "high",
                "location": "全文"
            })
            suggestions.append("建议添加争议解决方式条款")
            risk_score += 3
        
        if "不可抗力" not in contract_text:
            risk_points.append({
                "type": "missing_clause",
                "description": "缺少不可抗力条款",
                "severity": "low",
                "location": "全文"
            })
            suggestions.append("建议添加不可抗力条款")
            risk_score += 1
        
        # 检查模糊表述
        if "尽快" in contract_text or "及时" in contract_text:
            risk_points.append({
                "type": "ambiguous_term",
                "description": "存在模糊时间表述",
                "severity": "medium",
                "location": "时间条款"
            })
            suggestions.append("建议明确具体的时间期限")
            risk_score += 2
        
        # 确定风险等级
        if risk_score >= 5:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 3:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW
        
        return {
            "risk_level": risk_level,
            "risk_points": risk_points,
            "suggestions": suggestions,
            "risk_score": risk_score
        }
    
    async def get_contract_reviews(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[ContractReview], int]:
        """获取用户的合同审查记录"""
        
        # 获取总数
        from sqlalchemy import func
        count_query = select(func.count(ContractReview.id)).where(ContractReview.user_id == user_id)
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = select(ContractReview).where(ContractReview.user_id == user_id)\
                .order_by(desc(ContractReview.created_at))\
                .offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        reviews = result.scalars().all()
        
        return list(reviews), total
    
    async def get_contract_review(
        self,
        review_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[ContractReview]:
        """获取合同审查详情"""
        
        from sqlalchemy import and_
        
        result = await self.db.execute(
            select(ContractReview).where(
                and_(
                    ContractReview.id == review_id,
                    ContractReview.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_contract_templates(
        self,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取合同模板列表"""
        
        # TODO: 从MongoDB获取合同模板
        # 这里返回模拟数据
        
        templates = [
            {
                "id": "template_001",
                "name": "劳动合同模板",
                "category": "labor",
                "description": "标准劳动合同模板",
                "usage_count": 156,
                "rating": 4.5
            },
            {
                "id": "template_002",
                "name": "房屋租赁合同模板",
                "category": "lease",
                "description": "房屋租赁标准合同模板",
                "usage_count": 142,
                "rating": 4.3
            },
            {
                "id": "template_003",
                "name": "服务合同模板",
                "category": "service",
                "description": "通用服务合同模板",
                "usage_count": 98,
                "rating": 4.2
            }
        ]
        
        # 应用分类过滤
        if category:
            templates = [t for t in templates if t["category"] == category]
        
        # 应用分页
        total = len(templates)
        templates = templates[skip:skip + limit]
        
        return templates, total
