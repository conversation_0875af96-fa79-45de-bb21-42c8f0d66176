"""
法律文书生成服务
"""

import uuid
import os
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
import logging

from app.core.exceptions import ValidationException, ExternalServiceException, NotFoundException
from app.models.document import DocumentGeneration, DocumentStatus
from app.core.logging import business_logger
from app.core.config import settings

logger = logging.getLogger(__name__)


class DocumentGenerationService:
    """法律文书生成服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def generate_document(
        self,
        user_id: uuid.UUID,
        document_type: str,
        template_id: Optional[str] = None,
        input_data: Dict[str, Any] = None
    ) -> DocumentGeneration:
        """生成法律文书"""
        
        logger.info(f"开始生成文书: user_id={user_id}, type={document_type}")
        
        # 验证输入数据
        if not input_data:
            input_data = {}
        
        # 创建文书生成记录
        document_generation = DocumentGeneration(
            id=uuid.uuid4(),
            user_id=user_id,
            document_type=document_type,
            template_id=uuid.UUID(template_id) if template_id else None,
            input_data=input_data,
            status=DocumentStatus.PROCESSING
        )
        
        self.db.add(document_generation)
        await self.db.commit()
        await self.db.refresh(document_generation)
        
        try:
            # 获取模板内容
            template_content = await self._get_template_content(document_type, template_id)
            
            # 生成文书内容
            generated_content = await self._generate_document_content(
                template_content, input_data
            )
            
            # 保存生成的文书
            file_path = await self._save_generated_document(
                document_generation.id, generated_content, document_type
            )
            
            # 更新记录
            document_generation.generated_content = generated_content
            document_generation.file_path = file_path
            document_generation.status = DocumentStatus.COMPLETED
            
            await self.db.commit()
            await self.db.refresh(document_generation)
            
            # 记录业务日志
            business_logger.log_user_action(
                user_id=str(user_id),
                action="generate_document",
                resource_type="document",
                resource_id=str(document_generation.id),
                details={
                    "document_type": document_type,
                    "template_id": template_id,
                    "content_length": len(generated_content)
                }
            )
            
            logger.info(f"文书生成完成: document_id={document_generation.id}")
            
        except Exception as e:
            # 更新失败状态
            document_generation.status = DocumentStatus.FAILED
            await self.db.commit()
            
            logger.error(f"文书生成失败: {e}")
            raise ExternalServiceException("文书生成服务暂时不可用")
        
        return document_generation
    
    async def _get_template_content(self, document_type: str, template_id: Optional[str] = None) -> str:
        """获取模板内容"""
        
        # TODO: 从MongoDB获取模板内容
        # 这里返回模拟模板
        
        templates = {
            "complaint": """民事起诉状

原告：{{plaintiff_name}}，{{plaintiff_gender}}，{{plaintiff_birth_date}}出生，{{plaintiff_nationality}}，{{plaintiff_occupation}}，住址：{{plaintiff_address}}，联系电话：{{plaintiff_phone}}。

被告：{{defendant_name}}，{{defendant_gender}}，{{defendant_birth_date}}出生，{{defendant_nationality}}，{{defendant_occupation}}，住址：{{defendant_address}}，联系电话：{{defendant_phone}}。

诉讼请求：
{{requests}}

事实和理由：
{{facts_and_reasons}}

此致
{{court_name}}

原告：{{plaintiff_name}}
{{date}}
""",
            
            "contract": """{{contract_title}}

甲方：{{party_a_name}}
地址：{{party_a_address}}
联系电话：{{party_a_phone}}

乙方：{{party_b_name}}
地址：{{party_b_address}}
联系电话：{{party_b_phone}}

根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就{{contract_subject}}事宜达成如下协议：

第一条 合同标的
{{contract_object}}

第二条 合同价款
{{contract_price}}

第三条 履行期限
{{performance_period}}

第四条 违约责任
{{breach_liability}}

第五条 争议解决
{{dispute_resolution}}

第六条 其他约定
{{other_terms}}

本合同一式两份，甲乙双方各执一份，具有同等法律效力。

甲方（签字/盖章）：_____________    乙方（签字/盖章）：_____________

日期：{{date}}                      日期：{{date}}
""",
            
            "power_of_attorney": """委托书

委托人：{{client_name}}，{{client_gender}}，{{client_birth_date}}出生，{{client_nationality}}，{{client_occupation}}，住址：{{client_address}}，身份证号：{{client_id_number}}，联系电话：{{client_phone}}。

受托人：{{attorney_name}}，{{attorney_gender}}，{{attorney_birth_date}}出生，{{attorney_nationality}}，{{attorney_occupation}}，住址：{{attorney_address}}，身份证号：{{attorney_id_number}}，联系电话：{{attorney_phone}}。

委托事项：
{{delegation_matters}}

委托权限：
{{delegation_authority}}

委托期限：
{{delegation_period}}

委托人签字：_____________
受托人签字：_____________

日期：{{date}}
"""
        }
        
        return templates.get(document_type, "未找到对应的文书模板")
    
    async def _generate_document_content(self, template_content: str, input_data: Dict[str, Any]) -> str:
        """生成文书内容"""
        
        # 简单的模板替换
        content = template_content
        
        # 添加当前日期
        input_data["date"] = datetime.now().strftime("%Y年%m月%d日")
        
        # 替换模板变量
        for key, value in input_data.items():
            placeholder = f"{{{{{key}}}}}"
            content = content.replace(placeholder, str(value))
        
        # 处理未填充的占位符
        import re
        remaining_placeholders = re.findall(r'\{\{([^}]+)\}\}', content)
        for placeholder in remaining_placeholders:
            content = content.replace(f"{{{{{placeholder}}}}}", f"[请填写{placeholder}]")
        
        return content
    
    async def _save_generated_document(
        self, 
        document_id: uuid.UUID, 
        content: str, 
        document_type: str
    ) -> str:
        """保存生成的文书"""
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{document_type}_{timestamp}_{document_id}.txt"
        
        # 确保目录存在
        upload_dir = os.path.join(settings.UPLOAD_DIR, "documents")
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_dir, filename)
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return file_path
    
    async def get_document_generations(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[DocumentGeneration], int]:
        """获取用户的文书生成记录"""
        
        # 获取总数
        from sqlalchemy import func
        count_query = select(func.count(DocumentGeneration.id)).where(DocumentGeneration.user_id == user_id)
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = select(DocumentGeneration).where(DocumentGeneration.user_id == user_id)\
                .order_by(desc(DocumentGeneration.created_at))\
                .offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        generations = result.scalars().all()
        
        return list(generations), total
    
    async def get_document_generation(
        self,
        generation_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[DocumentGeneration]:
        """获取文书生成详情"""
        
        from sqlalchemy import and_
        
        result = await self.db.execute(
            select(DocumentGeneration).where(
                and_(
                    DocumentGeneration.id == generation_id,
                    DocumentGeneration.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_document_templates(
        self,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取文书模板列表"""
        
        # TODO: 从MongoDB获取文书模板
        # 这里返回模拟数据
        
        templates = [
            {
                "id": "template_complaint",
                "name": "民事起诉状",
                "category": "litigation",
                "description": "标准民事起诉状模板",
                "usage_count": 89,
                "rating": 4.6
            },
            {
                "id": "template_contract",
                "name": "通用合同模板",
                "category": "contract",
                "description": "通用合同协议模板",
                "usage_count": 156,
                "rating": 4.4
            },
            {
                "id": "template_power_of_attorney",
                "name": "委托书模板",
                "category": "authorization",
                "description": "标准委托书模板",
                "usage_count": 67,
                "rating": 4.3
            }
        ]
        
        # 应用分类过滤
        if category:
            templates = [t for t in templates if t["category"] == category]
        
        # 应用分页
        total = len(templates)
        templates = templates[skip:skip + limit]
        
        return templates, total
    
    async def download_document(self, generation_id: uuid.UUID, user_id: uuid.UUID) -> Optional[str]:
        """下载生成的文书"""
        
        generation = await self.get_document_generation(generation_id, user_id)
        if not generation or not generation.file_path:
            return None
        
        # 检查文件是否存在
        if not os.path.exists(generation.file_path):
            return None
        
        return generation.file_path
