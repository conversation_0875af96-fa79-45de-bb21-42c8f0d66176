"""
收藏和历史记录服务
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, and_, func
import logging

from app.core.exceptions import ValidationException, NotFoundException, ConflictException
from app.models.favorite import UserFavorite, ItemType
from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class FavoriteService:
    """收藏服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def add_favorite(
        self,
        user_id: uuid.UUID,
        item_type: ItemType,
        item_id: str,
        item_title: str,
        item_data: Optional[Dict[str, Any]] = None
    ) -> UserFavorite:
        """添加收藏"""
        
        logger.info(f"添加收藏: user_id={user_id}, type={item_type}, item_id={item_id}")
        
        # 检查是否已收藏
        existing = await self.get_favorite(user_id, item_type, item_id)
        if existing:
            raise ConflictException("该项目已收藏")
        
        # 创建收藏记录
        favorite = UserFavorite(
            id=uuid.uuid4(),
            user_id=user_id,
            item_type=item_type,
            item_id=item_id,
            item_title=item_title,
            item_data=item_data or {}
        )
        
        self.db.add(favorite)
        await self.db.commit()
        await self.db.refresh(favorite)
        
        # 记录业务日志
        business_logger.log_user_action(
            user_id=str(user_id),
            action="add_favorite",
            resource_type=item_type,
            resource_id=item_id,
            details={"item_title": item_title}
        )
        
        logger.info(f"收藏添加成功: favorite_id={favorite.id}")
        return favorite
    
    async def remove_favorite(
        self,
        user_id: uuid.UUID,
        item_type: ItemType,
        item_id: str
    ) -> bool:
        """移除收藏"""
        
        logger.info(f"移除收藏: user_id={user_id}, type={item_type}, item_id={item_id}")
        
        favorite = await self.get_favorite(user_id, item_type, item_id)
        if not favorite:
            raise NotFoundException("收藏记录不存在")
        
        await self.db.delete(favorite)
        await self.db.commit()
        
        # 记录业务日志
        business_logger.log_user_action(
            user_id=str(user_id),
            action="remove_favorite",
            resource_type=item_type,
            resource_id=item_id
        )
        
        logger.info("收藏移除成功")
        return True
    
    async def get_favorite(
        self,
        user_id: uuid.UUID,
        item_type: ItemType,
        item_id: str
    ) -> Optional[UserFavorite]:
        """获取收藏记录"""
        
        result = await self.db.execute(
            select(UserFavorite).where(
                and_(
                    UserFavorite.user_id == user_id,
                    UserFavorite.item_type == item_type,
                    UserFavorite.item_id == item_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_user_favorites(
        self,
        user_id: uuid.UUID,
        item_type: Optional[ItemType] = None,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[UserFavorite], int]:
        """获取用户收藏列表"""
        
        query = select(UserFavorite).where(UserFavorite.user_id == user_id)
        
        # 添加类型过滤
        if item_type:
            query = query.where(UserFavorite.item_type == item_type)
        
        # 获取总数
        count_query = select(func.count(UserFavorite.id)).where(UserFavorite.user_id == user_id)
        if item_type:
            count_query = count_query.where(UserFavorite.item_type == item_type)
        
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = query.order_by(desc(UserFavorite.created_at)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        favorites = result.scalars().all()
        
        return list(favorites), total
    
    async def is_favorited(
        self,
        user_id: uuid.UUID,
        item_type: ItemType,
        item_id: str
    ) -> bool:
        """检查是否已收藏"""
        
        favorite = await self.get_favorite(user_id, item_type, item_id)
        return favorite is not None
    
    async def get_favorite_statistics(self, user_id: uuid.UUID) -> Dict[str, Any]:
        """获取用户收藏统计"""
        
        # 按类型统计
        type_stats = {}
        for item_type in ItemType:
            count_result = await self.db.execute(
                select(func.count(UserFavorite.id)).where(
                    and_(
                        UserFavorite.user_id == user_id,
                        UserFavorite.item_type == item_type
                    )
                )
            )
            type_stats[item_type.value] = count_result.scalar()
        
        # 总数统计
        total_result = await self.db.execute(
            select(func.count(UserFavorite.id)).where(UserFavorite.user_id == user_id)
        )
        total = total_result.scalar()
        
        return {
            "total_favorites": total,
            "by_type": type_stats
        }


class HistoryService:
    """历史记录服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_qa_history(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户问答历史"""
        
        from app.models.qa import QARecord
        
        # 获取总数
        count_result = await self.db.execute(
            select(func.count(QARecord.id)).where(QARecord.user_id == user_id)
        )
        total = count_result.scalar()
        
        # 获取分页数据
        result = await self.db.execute(
            select(QARecord)
            .where(QARecord.user_id == user_id)
            .order_by(desc(QARecord.created_at))
            .offset(skip)
            .limit(limit)
        )
        records = result.scalars().all()
        
        # 转换为字典格式
        history = []
        for record in records:
            history.append({
                "id": str(record.id),
                "question": record.question,
                "answer": record.answer,
                "category": record.category,
                "created_at": record.created_at,
                "type": "qa"
            })
        
        return history, total
    
    async def get_user_contract_history(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户合同分析历史"""
        
        from app.models.contract import ContractReview
        
        # 获取总数
        count_result = await self.db.execute(
            select(func.count(ContractReview.id)).where(ContractReview.user_id == user_id)
        )
        total = count_result.scalar()
        
        # 获取分页数据
        result = await self.db.execute(
            select(ContractReview)
            .where(ContractReview.user_id == user_id)
            .order_by(desc(ContractReview.created_at))
            .offset(skip)
            .limit(limit)
        )
        records = result.scalars().all()
        
        # 转换为字典格式
        history = []
        for record in records:
            history.append({
                "id": str(record.id),
                "contract_name": record.contract_name,
                "contract_type": record.contract_type,
                "risk_level": record.risk_level,
                "created_at": record.created_at,
                "type": "contract"
            })
        
        return history, total
    
    async def get_user_document_history(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户文书生成历史"""
        
        from app.models.document import DocumentGeneration
        
        # 获取总数
        count_result = await self.db.execute(
            select(func.count(DocumentGeneration.id)).where(DocumentGeneration.user_id == user_id)
        )
        total = count_result.scalar()
        
        # 获取分页数据
        result = await self.db.execute(
            select(DocumentGeneration)
            .where(DocumentGeneration.user_id == user_id)
            .order_by(desc(DocumentGeneration.created_at))
            .offset(skip)
            .limit(limit)
        )
        records = result.scalars().all()
        
        # 转换为字典格式
        history = []
        for record in records:
            history.append({
                "id": str(record.id),
                "document_type": record.document_type,
                "status": record.status,
                "created_at": record.created_at,
                "type": "document"
            })
        
        return history, total
    
    async def get_user_all_history(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户所有历史记录"""
        
        # 获取各类历史记录
        qa_history, _ = await self.get_user_qa_history(user_id, 0, 100)
        contract_history, _ = await self.get_user_contract_history(user_id, 0, 100)
        document_history, _ = await self.get_user_document_history(user_id, 0, 100)
        
        # 合并并按时间排序
        all_history = qa_history + contract_history + document_history
        all_history.sort(key=lambda x: x["created_at"], reverse=True)
        
        # 应用分页
        total = len(all_history)
        paginated_history = all_history[skip:skip + limit]
        
        return paginated_history, total
