"""
AI问答服务
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, and_
import logging

from app.core.exceptions import ValidationException, ExternalServiceException
from app.models.qa import QARecord, QAStatus
from app.models.user import User
from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class QAService:
    """AI问答服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def ask_question(
        self,
        user_id: uuid.UUID,
        question: str,
        category: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> QARecord:
        """处理用户提问"""
        
        logger.info(f"处理用户提问: user_id={user_id}, question_length={len(question)}")
        
        # 验证问题内容
        if not question or len(question.strip()) < 5:
            raise ValidationException("问题内容过短，请详细描述您的法律问题")
        
        if len(question) > 2000:
            raise ValidationException("问题内容过长，请控制在2000字以内")
        
        # 创建问答记录
        qa_record = QARecord(
            id=uuid.uuid4(),
            user_id=user_id,
            session_id=session_id or str(uuid.uuid4()),
            question=question.strip(),
            category=category,
            status=QAStatus.PENDING
        )
        
        self.db.add(qa_record)
        await self.db.commit()
        await self.db.refresh(qa_record)
        
        try:
            # 调用AI服务生成回答
            answer_result = await self._generate_answer(question, category)
            
            # 更新记录
            qa_record.answer = answer_result["answer"]
            qa_record.confidence_score = answer_result.get("confidence_score", 0.8)
            qa_record.status = QAStatus.COMPLETED
            qa_record.metadata = {
                "processing_time": answer_result.get("processing_time", 0),
                "model_version": answer_result.get("model_version", "v1.0"),
                "sources": answer_result.get("sources", [])
            }
            
            await self.db.commit()
            await self.db.refresh(qa_record)
            
            # 记录业务日志
            business_logger.log_user_action(
                user_id=str(user_id),
                action="ask_question",
                resource_type="qa",
                resource_id=str(qa_record.id),
                details={
                    "category": category,
                    "question_length": len(question),
                    "confidence_score": qa_record.confidence_score
                }
            )
            
            logger.info(f"问答处理完成: qa_id={qa_record.id}")
            
        except Exception as e:
            # 更新失败状态
            qa_record.status = QAStatus.FAILED
            qa_record.metadata = {"error": str(e)}
            await self.db.commit()
            
            logger.error(f"问答处理失败: {e}")
            raise ExternalServiceException("AI服务暂时不可用，请稍后重试")
        
        return qa_record
    
    async def _generate_answer(self, question: str, category: Optional[str] = None) -> Dict[str, Any]:
        """生成AI回答"""
        
        # TODO: 集成实际的AI模型
        # 这里是模拟实现
        
        import time
        start_time = time.time()
        
        # 模拟AI处理时间
        await asyncio.sleep(1)
        
        # 根据问题类型生成不同的回答
        if "合同" in question:
            answer = """根据您的问题，关于合同相关的法律问题，我建议您注意以下几点：

1. **合同的有效性**：确保合同符合法律规定的有效要件
2. **合同条款**：仔细审查合同条款，特别是违约责任条款
3. **履行义务**：明确双方的权利义务关系
4. **争议解决**：约定明确的争议解决方式

如需更详细的法律建议，建议咨询专业律师。"""
            
        elif "劳动" in question or "工作" in question:
            answer = """关于劳动法相关问题，请注意：

1. **劳动合同**：确保签订书面劳动合同
2. **工资待遇**：了解最低工资标准和加班费计算
3. **工作时间**：标准工时制度和休息休假权利
4. **社会保险**：用人单位应依法缴纳社会保险

具体情况建议咨询劳动监察部门或专业律师。"""
            
        else:
            answer = """感谢您的咨询。根据您描述的情况，建议您：

1. **收集证据**：保留相关的文件、合同、聊天记录等证据材料
2. **了解法律**：查阅相关法律法规，了解自己的权利义务
3. **寻求帮助**：可以咨询法律援助机构或专业律师
4. **及时行动**：注意诉讼时效，及时维护自己的合法权益

如需更专业的法律意见，建议咨询执业律师。"""
        
        processing_time = time.time() - start_time
        
        return {
            "answer": answer,
            "confidence_score": 0.85,
            "processing_time": processing_time,
            "model_version": "legal-assistant-v1.0",
            "sources": ["中华人民共和国民法典", "相关司法解释"]
        }
    
    async def get_qa_history(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None
    ) -> tuple[List[QARecord], int]:
        """获取用户问答历史"""
        
        query = select(QARecord).where(QARecord.user_id == user_id)
        
        # 添加分类过滤
        if category:
            query = query.where(QARecord.category == category)
        
        # 获取总数
        from sqlalchemy import func
        count_query = select(func.count(QARecord.id)).where(QARecord.user_id == user_id)
        if category:
            count_query = count_query.where(QARecord.category == category)
        
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = query.order_by(desc(QARecord.created_at)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        records = result.scalars().all()
        
        return list(records), total
    
    async def get_qa_record(self, record_id: uuid.UUID, user_id: uuid.UUID) -> Optional[QARecord]:
        """获取问答记录详情"""
        
        result = await self.db.execute(
            select(QARecord).where(
                and_(
                    QARecord.id == record_id,
                    QARecord.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def submit_feedback(
        self,
        record_id: uuid.UUID,
        user_id: uuid.UUID,
        feedback_score: int,
        feedback_comment: Optional[str] = None
    ) -> bool:
        """提交用户反馈"""
        
        if feedback_score < 1 or feedback_score > 5:
            raise ValidationException("评分必须在1-5之间")
        
        result = await self.db.execute(
            select(QARecord).where(
                and_(
                    QARecord.id == record_id,
                    QARecord.user_id == user_id
                )
            )
        )
        record = result.scalar_one_or_none()
        
        if not record:
            return False
        
        # 更新反馈
        record.feedback_score = feedback_score
        if feedback_comment:
            if not record.metadata:
                record.metadata = {}
            record.metadata["feedback_comment"] = feedback_comment
        
        await self.db.commit()
        
        # 记录业务日志
        business_logger.log_user_action(
            user_id=str(user_id),
            action="submit_feedback",
            resource_type="qa",
            resource_id=str(record_id),
            details={
                "feedback_score": feedback_score,
                "has_comment": bool(feedback_comment)
            }
        )
        
        return True
    
    async def get_popular_questions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门问题"""
        
        # TODO: 实现基于统计的热门问题查询
        # 这里返回模拟数据
        
        popular_questions = [
            {
                "question": "如何解除劳动合同？",
                "category": "劳动法",
                "ask_count": 156
            },
            {
                "question": "房屋买卖合同纠纷如何处理？",
                "category": "合同法",
                "ask_count": 142
            },
            {
                "question": "交通事故责任如何认定？",
                "category": "交通法",
                "ask_count": 128
            },
            {
                "question": "离婚财产如何分割？",
                "category": "婚姻法",
                "ask_count": 115
            },
            {
                "question": "公司拖欠工资怎么办？",
                "category": "劳动法",
                "ask_count": 98
            }
        ]
        
        return popular_questions[:limit]


# 导入asyncio用于模拟异步操作
import asyncio
