"""
基于角色的访问控制(RBAC)权限系统
"""

from enum import Enum
from typing import List, Dict, Set, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"           # 系统管理员
    LAWYER = "lawyer"         # 律师用户
    ENTERPRISE = "enterprise" # 企业用户
    INDIVIDUAL = "individual" # 个人用户
    GUEST = "guest"          # 访客用户


class Permission(str, Enum):
    """权限枚举"""
    # 用户管理权限
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    USER_ADMIN = "user:admin"
    
    # 问答系统权限
    QA_ASK = "qa:ask"
    QA_READ = "qa:read"
    QA_WRITE = "qa:write"
    QA_DELETE = "qa:delete"
    QA_ADMIN = "qa:admin"
    
    # 案例检索权限
    CASE_SEARCH = "case:search"
    CASE_READ = "case:read"
    CASE_EXPORT = "case:export"
    CASE_ADMIN = "case:admin"
    
    # 合同工具权限
    CONTRACT_ANALYZE = "contract:analyze"
    CONTRACT_READ = "contract:read"
    CONTRACT_WRITE = "contract:write"
    CONTRACT_DELETE = "contract:delete"
    CONTRACT_TEMPLATE = "contract:template"
    CONTRACT_ADMIN = "contract:admin"
    
    # 文书工具权限
    DOCUMENT_GENERATE = "document:generate"
    DOCUMENT_READ = "document:read"
    DOCUMENT_WRITE = "document:write"
    DOCUMENT_DELETE = "document:delete"
    DOCUMENT_TEMPLATE = "document:template"
    DOCUMENT_ADMIN = "document:admin"
    
    # 纠纷解决权限
    DISPUTE_ANALYZE = "dispute:analyze"
    DISPUTE_READ = "dispute:read"
    DISPUTE_ADMIN = "dispute:admin"
    
    # 收藏和历史权限
    FAVORITE_READ = "favorite:read"
    FAVORITE_WRITE = "favorite:write"
    FAVORITE_DELETE = "favorite:delete"
    
    HISTORY_READ = "history:read"
    HISTORY_DELETE = "history:delete"
    
    # 系统管理权限
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_BACKUP = "system:backup"
    SYSTEM_ADMIN = "system:admin"


@dataclass
class RolePermissions:
    """角色权限配置"""
    role: UserRole
    permissions: Set[Permission]
    description: str


# 角色权限映射配置
ROLE_PERMISSIONS_MAP: Dict[UserRole, RolePermissions] = {
    # 访客用户 - 最基础权限
    UserRole.GUEST: RolePermissions(
        role=UserRole.GUEST,
        permissions={
            Permission.QA_ASK,
            Permission.CASE_SEARCH,
            Permission.CASE_READ,
        },
        description="访客用户，仅可进行基础查询"
    ),
    
    # 个人用户 - 基础功能权限
    UserRole.INDIVIDUAL: RolePermissions(
        role=UserRole.INDIVIDUAL,
        permissions={
            Permission.USER_READ,
            Permission.USER_WRITE,
            Permission.QA_ASK,
            Permission.QA_READ,
            Permission.QA_WRITE,
            Permission.CASE_SEARCH,
            Permission.CASE_READ,
            Permission.CONTRACT_ANALYZE,
            Permission.CONTRACT_READ,
            Permission.CONTRACT_WRITE,
            Permission.DOCUMENT_GENERATE,
            Permission.DOCUMENT_READ,
            Permission.DOCUMENT_WRITE,
            Permission.DISPUTE_ANALYZE,
            Permission.DISPUTE_READ,
            Permission.FAVORITE_READ,
            Permission.FAVORITE_WRITE,
            Permission.FAVORITE_DELETE,
            Permission.HISTORY_READ,
        },
        description="个人用户，可使用基础法律服务功能"
    ),
    
    # 企业用户 - 增强功能权限
    UserRole.ENTERPRISE: RolePermissions(
        role=UserRole.ENTERPRISE,
        permissions={
            Permission.USER_READ,
            Permission.USER_WRITE,
            Permission.QA_ASK,
            Permission.QA_READ,
            Permission.QA_WRITE,
            Permission.CASE_SEARCH,
            Permission.CASE_READ,
            Permission.CASE_EXPORT,
            Permission.CONTRACT_ANALYZE,
            Permission.CONTRACT_READ,
            Permission.CONTRACT_WRITE,
            Permission.CONTRACT_TEMPLATE,
            Permission.DOCUMENT_GENERATE,
            Permission.DOCUMENT_READ,
            Permission.DOCUMENT_WRITE,
            Permission.DOCUMENT_TEMPLATE,
            Permission.DISPUTE_ANALYZE,
            Permission.DISPUTE_READ,
            Permission.FAVORITE_READ,
            Permission.FAVORITE_WRITE,
            Permission.FAVORITE_DELETE,
            Permission.HISTORY_READ,
            Permission.HISTORY_DELETE,
        },
        description="企业用户，可使用高级法律服务功能"
    ),
    
    # 律师用户 - 专业功能权限
    UserRole.LAWYER: RolePermissions(
        role=UserRole.LAWYER,
        permissions={
            Permission.USER_READ,
            Permission.USER_WRITE,
            Permission.QA_ASK,
            Permission.QA_READ,
            Permission.QA_WRITE,
            Permission.QA_DELETE,
            Permission.CASE_SEARCH,
            Permission.CASE_READ,
            Permission.CASE_EXPORT,
            Permission.CONTRACT_ANALYZE,
            Permission.CONTRACT_READ,
            Permission.CONTRACT_WRITE,
            Permission.CONTRACT_DELETE,
            Permission.CONTRACT_TEMPLATE,
            Permission.DOCUMENT_GENERATE,
            Permission.DOCUMENT_READ,
            Permission.DOCUMENT_WRITE,
            Permission.DOCUMENT_DELETE,
            Permission.DOCUMENT_TEMPLATE,
            Permission.DISPUTE_ANALYZE,
            Permission.DISPUTE_READ,
            Permission.FAVORITE_READ,
            Permission.FAVORITE_WRITE,
            Permission.FAVORITE_DELETE,
            Permission.HISTORY_READ,
            Permission.HISTORY_DELETE,
        },
        description="律师用户，可使用专业法律服务功能"
    ),
    
    # 系统管理员 - 所有权限
    UserRole.ADMIN: RolePermissions(
        role=UserRole.ADMIN,
        permissions=set(Permission),  # 所有权限
        description="系统管理员，拥有所有权限"
    ),
}


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def has_permission(user_role: UserRole, required_permission: Permission) -> bool:
        """检查用户角色是否具有指定权限"""
        role_permissions = ROLE_PERMISSIONS_MAP.get(user_role)
        if not role_permissions:
            logger.warning(f"未知用户角色: {user_role}")
            return False
        
        has_perm = required_permission in role_permissions.permissions
        logger.debug(f"权限检查: role={user_role}, permission={required_permission}, result={has_perm}")
        return has_perm
    
    @staticmethod
    def has_any_permission(user_role: UserRole, required_permissions: List[Permission]) -> bool:
        """检查用户角色是否具有任一指定权限"""
        return any(
            PermissionChecker.has_permission(user_role, perm) 
            for perm in required_permissions
        )
    
    @staticmethod
    def has_all_permissions(user_role: UserRole, required_permissions: List[Permission]) -> bool:
        """检查用户角色是否具有所有指定权限"""
        return all(
            PermissionChecker.has_permission(user_role, perm) 
            for perm in required_permissions
        )
    
    @staticmethod
    def get_user_permissions(user_role: UserRole) -> Set[Permission]:
        """获取用户角色的所有权限"""
        role_permissions = ROLE_PERMISSIONS_MAP.get(user_role)
        if not role_permissions:
            return set()
        return role_permissions.permissions.copy()
    
    @staticmethod
    def can_access_resource(user_role: UserRole, resource_type: str, action: str) -> bool:
        """检查用户是否可以访问特定资源"""
        permission_str = f"{resource_type}:{action}"
        try:
            required_permission = Permission(permission_str)
            return PermissionChecker.has_permission(user_role, required_permission)
        except ValueError:
            logger.warning(f"未知权限: {permission_str}")
            return False


class ResourceOwnershipChecker:
    """资源所有权检查器"""
    
    @staticmethod
    def is_resource_owner(user_id: str, resource_owner_id: str) -> bool:
        """检查用户是否为资源所有者"""
        return user_id == resource_owner_id
    
    @staticmethod
    def can_access_own_resource(
        user_role: UserRole, 
        user_id: str, 
        resource_owner_id: str,
        base_permission: Permission
    ) -> bool:
        """检查用户是否可以访问自己的资源"""
        # 管理员可以访问所有资源
        if user_role == UserRole.ADMIN:
            return True
        
        # 检查基础权限
        if not PermissionChecker.has_permission(user_role, base_permission):
            return False
        
        # 检查资源所有权
        return ResourceOwnershipChecker.is_resource_owner(user_id, resource_owner_id)


def require_permission(required_permission: Permission):
    """权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 这里应该从请求上下文中获取用户角色
            # 实际实现中需要与FastAPI的依赖注入系统集成
            return func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(required_permissions: List[Permission]):
    """任一权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator


def require_all_permissions(required_permissions: List[Permission]):
    """所有权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator
