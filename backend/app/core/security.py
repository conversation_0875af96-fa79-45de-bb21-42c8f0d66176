"""
安全相关功能模块
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Union, Optional
from jose import jwt, JWTError
from passlib.context import CryptContext
from passlib.hash import bcrypt
import logging

from app.core.config import settings
from app.core.exceptions import AuthenticationException

logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    
    logger.info(f"创建访问令牌成功: subject={subject}")
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """创建刷新令牌"""
    expire = datetime.utcnow() + timedelta(days=30)  # 刷新令牌30天有效
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    
    logger.info(f"创建刷新令牌成功: subject={subject}")
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """验证令牌并返回用户ID"""
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("令牌中缺少用户ID")
            return None
        
        # 检查令牌是否过期
        exp = payload.get("exp")
        if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
            logger.warning("令牌已过期")
            return None
            
        return user_id
        
    except JWTError as e:
        logger.error(f"令牌验证失败: {e}")
        return None


def verify_refresh_token(token: str) -> Optional[str]:
    """验证刷新令牌并返回用户ID"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # 检查令牌类型
        token_type = payload.get("type")
        if token_type != "refresh":
            logger.warning("不是刷新令牌")
            return None
        
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("刷新令牌中缺少用户ID")
            return None
            
        return user_id
        
    except JWTError as e:
        logger.error(f"刷新令牌验证失败: {e}")
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"密码验证失败: {e}")
        return False


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """生成密码重置令牌"""
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """验证密码重置令牌"""
    try:
        decoded_token = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_session_token() -> str:
    """生成会话令牌"""
    return secrets.token_urlsafe(32)


def generate_api_key() -> str:
    """生成API密钥"""
    return secrets.token_urlsafe(32)


class PasswordValidator:
    """密码验证器"""
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, list[str]]:
        """验证密码强度"""
        errors = []
        
        # 长度检查
        if len(password) < 8:
            errors.append("密码长度至少8位")
        
        # 字符类型检查
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if not has_upper:
            errors.append("密码必须包含大写字母")
        if not has_lower:
            errors.append("密码必须包含小写字母")
        if not has_digit:
            errors.append("密码必须包含数字")
        if not has_special:
            errors.append("密码必须包含特殊字符")
        
        # 常见密码检查
        common_passwords = [
            "password", "123456", "123456789", "qwerty", 
            "abc123", "password123", "admin", "root"
        ]
        if password.lower() in common_passwords:
            errors.append("不能使用常见密码")
        
        return len(errors) == 0, errors


class TokenBlacklist:
    """令牌黑名单管理"""
    
    def __init__(self):
        self._blacklist = set()
    
    def add_token(self, token: str) -> None:
        """添加令牌到黑名单"""
        self._blacklist.add(token)
        logger.info(f"令牌已添加到黑名单")
    
    def is_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        return token in self._blacklist
    
    def remove_token(self, token: str) -> None:
        """从黑名单中移除令牌"""
        self._blacklist.discard(token)
        logger.info(f"令牌已从黑名单中移除")
    
    def clear(self) -> None:
        """清空黑名单"""
        self._blacklist.clear()
        logger.info("令牌黑名单已清空")


# 全局令牌黑名单实例
token_blacklist = TokenBlacklist()


def create_email_verification_token(email: str) -> str:
    """创建邮箱验证令牌"""
    delta = timedelta(hours=24)  # 24小时有效
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "email_verification"},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_email_verification_token(token: str) -> Optional[str]:
    """验证邮箱验证令牌"""
    try:
        decoded_token = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # 检查令牌类型
        if decoded_token.get("type") != "email_verification":
            return None
            
        return decoded_token["sub"]
    except JWTError:
        return None


def create_phone_verification_code() -> str:
    """创建手机验证码"""
    import random
    return str(random.randint(100000, 999999))


def hash_sensitive_data(data: str) -> str:
    """哈希敏感数据"""
    return bcrypt.hash(data)


def verify_sensitive_data(data: str, hashed_data: str) -> bool:
    """验证敏感数据"""
    try:
        return bcrypt.verify(data, hashed_data)
    except Exception:
        return False
