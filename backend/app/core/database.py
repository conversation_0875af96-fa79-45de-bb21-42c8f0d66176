"""
数据库连接和配置模块
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import Static<PERSON><PERSON>
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient
from elasticsearch import AsyncElasticsearch
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy基础类
Base = declarative_base()

# 元数据
metadata = MetaData()

# 数据库引擎
engine = None
async_engine = None

# 会话工厂
SessionLocal = None
AsyncSessionLocal = None

# Redis连接
redis_client: Optional[redis.Redis] = None

# MongoDB连接
mongodb_client: Optional[AsyncIOMotorClient] = None
mongodb_database = None

# Elasticsearch连接
elasticsearch_client: Optional[AsyncElasticsearch] = None


def create_database_engine():
    """创建数据库引擎"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    # 同步引擎
    engine = create_engine(
        settings.DATABASE_URL,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        echo=settings.DEBUG,
    )
    
    # 异步引擎
    async_database_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    async_engine = create_async_engine(
        async_database_url,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT,
        pool_recycle=settings.DB_POOL_RECYCLE,
        echo=settings.DEBUG,
    )
    
    # 会话工厂
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
    )
    
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    logger.info("✅ 数据库引擎创建完成")


async def create_redis_connection():
    """创建Redis连接"""
    global redis_client
    
    try:
        redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            max_connections=20,
        )
        
        # 测试连接
        await redis_client.ping()
        logger.info("✅ Redis连接创建完成")
        
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        redis_client = None


async def create_mongodb_connection():
    """创建MongoDB连接"""
    global mongodb_client, mongodb_database
    
    try:
        mongodb_client = AsyncIOMotorClient(settings.MONGODB_URL)
        
        # 获取数据库
        db_name = settings.MONGODB_URL.split("/")[-1]
        mongodb_database = mongodb_client[db_name]
        
        # 测试连接
        await mongodb_client.admin.command('ping')
        logger.info("✅ MongoDB连接创建完成")
        
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        mongodb_client = None
        mongodb_database = None


async def create_elasticsearch_connection():
    """创建Elasticsearch连接"""
    global elasticsearch_client
    
    try:
        elasticsearch_client = AsyncElasticsearch(
            [settings.ELASTICSEARCH_URL],
            timeout=30,
            max_retries=3,
            retry_on_timeout=True,
        )
        
        # 测试连接
        info = await elasticsearch_client.info()
        logger.info(f"✅ Elasticsearch连接创建完成: {info['version']['number']}")
        
    except Exception as e:
        logger.error(f"❌ Elasticsearch连接失败: {e}")
        elasticsearch_client = None


async def init_db():
    """初始化数据库连接"""
    logger.info("🔄 初始化数据库连接...")
    
    # 创建数据库引擎
    create_database_engine()
    
    # 创建异步连接
    await asyncio.gather(
        create_redis_connection(),
        create_mongodb_connection(),
        create_elasticsearch_connection(),
        return_exceptions=True,
    )
    
    logger.info("✅ 数据库连接初始化完成")


async def close_db():
    """关闭数据库连接"""
    logger.info("🔄 关闭数据库连接...")
    
    # 关闭Redis连接
    if redis_client:
        await redis_client.close()
        logger.info("✅ Redis连接已关闭")
    
    # 关闭MongoDB连接
    if mongodb_client:
        mongodb_client.close()
        logger.info("✅ MongoDB连接已关闭")
    
    # 关闭Elasticsearch连接
    if elasticsearch_client:
        await elasticsearch_client.close()
        logger.info("✅ Elasticsearch连接已关闭")
    
    # 关闭SQLAlchemy引擎
    if async_engine:
        await async_engine.dispose()
        logger.info("✅ SQLAlchemy异步引擎已关闭")
    
    if engine:
        engine.dispose()
        logger.info("✅ SQLAlchemy引擎已关闭")


# 依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_redis() -> redis.Redis:
    """获取Redis客户端"""
    if redis_client is None:
        raise RuntimeError("Redis连接未初始化")
    return redis_client


def get_mongodb():
    """获取MongoDB数据库"""
    if mongodb_database is None:
        raise RuntimeError("MongoDB连接未初始化")
    return mongodb_database


async def get_elasticsearch() -> AsyncElasticsearch:
    """获取Elasticsearch客户端"""
    if elasticsearch_client is None:
        raise RuntimeError("Elasticsearch连接未初始化")
    return elasticsearch_client


# 数据库健康检查
async def check_database_health() -> dict:
    """检查数据库健康状态"""
    health_status = {
        "postgresql": False,
        "redis": False,
        "mongodb": False,
        "elasticsearch": False,
    }
    
    # 检查PostgreSQL
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            health_status["postgresql"] = True
    except Exception as e:
        logger.error(f"PostgreSQL健康检查失败: {e}")
    
    # 检查Redis
    try:
        if redis_client:
            await redis_client.ping()
            health_status["redis"] = True
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
    
    # 检查MongoDB
    try:
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            health_status["mongodb"] = True
    except Exception as e:
        logger.error(f"MongoDB健康检查失败: {e}")
    
    # 检查Elasticsearch
    try:
        if elasticsearch_client:
            await elasticsearch_client.ping()
            health_status["elasticsearch"] = True
    except Exception as e:
        logger.error(f"Elasticsearch健康检查失败: {e}")
    
    return health_status


# 数据库事务装饰器
class DatabaseTransaction:
    """数据库事务上下文管理器"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.session.rollback()
        else:
            await self.session.commit()


async def get_db_transaction() -> AsyncGenerator[DatabaseTransaction, None]:
    """获取数据库事务"""
    async with AsyncSessionLocal() as session:
        try:
            yield DatabaseTransaction(session)
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
