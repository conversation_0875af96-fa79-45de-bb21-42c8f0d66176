"""
自定义异常模块
"""

from typing import Any, Dict, Optional


class LegalAssistantException(Exception):
    """AI法律助手基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: int = 400,
        status_code: int = 400,
        detail: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.detail = detail or {}
        super().__init__(self.message)


class ValidationException(LegalAssistantException):
    """数据验证异常"""
    
    def __init__(self, message: str = "数据验证失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40001,
            status_code=400,
            detail=detail,
        )


class AuthenticationException(LegalAssistantException):
    """身份认证异常"""
    
    def __init__(self, message: str = "身份认证失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40101,
            status_code=401,
            detail=detail,
        )


class AuthorizationException(LegalAssistantException):
    """权限授权异常"""
    
    def __init__(self, message: str = "权限不足", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40301,
            status_code=403,
            detail=detail,
        )


class NotFoundException(LegalAssistantException):
    """资源未找到异常"""
    
    def __init__(self, message: str = "资源未找到", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40401,
            status_code=404,
            detail=detail,
        )


class ConflictException(LegalAssistantException):
    """资源冲突异常"""
    
    def __init__(self, message: str = "资源冲突", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40901,
            status_code=409,
            detail=detail,
        )


class RateLimitException(LegalAssistantException):
    """请求频率限制异常"""
    
    def __init__(self, message: str = "请求过于频繁", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=42901,
            status_code=429,
            detail=detail,
        )


class InternalServerException(LegalAssistantException):
    """服务器内部异常"""
    
    def __init__(self, message: str = "服务器内部错误", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=50001,
            status_code=500,
            detail=detail,
        )


class DatabaseException(LegalAssistantException):
    """数据库异常"""
    
    def __init__(self, message: str = "数据库操作失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=50002,
            status_code=500,
            detail=detail,
        )


class ExternalServiceException(LegalAssistantException):
    """外部服务异常"""
    
    def __init__(self, message: str = "外部服务调用失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=50003,
            status_code=500,
            detail=detail,
        )


class BusinessLogicException(LegalAssistantException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=40002,
            status_code=400,
            detail=detail,
        )
