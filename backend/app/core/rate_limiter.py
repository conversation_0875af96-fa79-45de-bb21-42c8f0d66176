"""
API限流器
"""

import time
import redis
from typing import Optional, Dict, Any
from fastapi import HTTPException, Request
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """API限流器"""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis_client = redis_client or redis.Redis.from_url(settings.REDIS_URL)
        
        # 限流规则配置
        self.rate_limits = {
            "default": {"requests": 100, "window": 3600},  # 默认：每小时100次
            "auth": {"requests": 10, "window": 900},        # 认证：每15分钟10次
            "qa": {"requests": 50, "window": 3600},         # 问答：每小时50次
            "contract": {"requests": 20, "window": 3600},   # 合同：每小时20次
            "document": {"requests": 10, "window": 3600},   # 文书：每小时10次
        }
    
    def check_rate_limit(
        self, 
        key: str, 
        limit_type: str = "default",
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """检查限流状态"""
        
        # 获取限流配置
        config = self.rate_limits.get(limit_type, self.rate_limits["default"])
        max_requests = config["requests"]
        window_seconds = config["window"]
        
        # 构建Redis键
        redis_key = f"rate_limit:{limit_type}:{key}"
        
        try:
            # 使用滑动窗口算法
            current_time = int(time.time())
            window_start = current_time - window_seconds
            
            # 清理过期记录
            self.redis_client.zremrangebyscore(redis_key, 0, window_start)
            
            # 获取当前窗口内的请求数
            current_requests = self.redis_client.zcard(redis_key)
            
            # 检查是否超过限制
            if current_requests >= max_requests:
                # 获取最早的请求时间
                oldest_request = self.redis_client.zrange(redis_key, 0, 0, withscores=True)
                if oldest_request:
                    reset_time = int(oldest_request[0][1]) + window_seconds
                else:
                    reset_time = current_time + window_seconds
                
                return {
                    "allowed": False,
                    "limit": max_requests,
                    "remaining": 0,
                    "reset_time": reset_time,
                    "retry_after": reset_time - current_time
                }
            
            # 记录当前请求
            self.redis_client.zadd(redis_key, {str(current_time): current_time})
            self.redis_client.expire(redis_key, window_seconds)
            
            remaining = max_requests - current_requests - 1
            
            return {
                "allowed": True,
                "limit": max_requests,
                "remaining": remaining,
                "reset_time": current_time + window_seconds,
                "retry_after": 0
            }
            
        except Exception as e:
            logger.error(f"限流检查失败: {e}")
            # 限流器故障时允许请求通过
            return {
                "allowed": True,
                "limit": max_requests,
                "remaining": max_requests,
                "reset_time": current_time + window_seconds,
                "retry_after": 0
            }
    
    def get_client_key(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        if hasattr(request.state, 'user') and request.state.user:
            return f"user:{request.state.user.id}"
        
        # 使用IP地址
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"


# 全局限流器实例
rate_limiter = RateLimiter()


def create_rate_limit_middleware(limit_type: str = "default"):
    """创建限流中间件"""
    
    async def rate_limit_middleware(request: Request, call_next):
        # 获取客户端标识
        client_key = rate_limiter.get_client_key(request)
        
        # 检查限流
        result = rate_limiter.check_rate_limit(client_key, limit_type)
        
        if not result["allowed"]:
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后再试",
                headers={
                    "X-RateLimit-Limit": str(result["limit"]),
                    "X-RateLimit-Remaining": str(result["remaining"]),
                    "X-RateLimit-Reset": str(result["reset_time"]),
                    "Retry-After": str(result["retry_after"])
                }
            )
        
        # 添加限流头信息
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(result["limit"])
        response.headers["X-RateLimit-Remaining"] = str(result["remaining"])
        response.headers["X-RateLimit-Reset"] = str(result["reset_time"])
        
        return response
    
    return rate_limit_middleware


def rate_limit(limit_type: str = "default"):
    """限流装饰器"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            client_key = rate_limiter.get_client_key(request)
            result = rate_limiter.check_rate_limit(client_key, limit_type)
            
            if not result["allowed"]:
                raise HTTPException(
                    status_code=429,
                    detail="请求过于频繁，请稍后再试",
                    headers={
                        "X-RateLimit-Limit": str(result["limit"]),
                        "X-RateLimit-Remaining": str(result["remaining"]),
                        "X-RateLimit-Reset": str(result["reset_time"]),
                        "Retry-After": str(result["retry_after"])
                    }
                )
            
            return await func(request, *args, **kwargs)
        
        return wrapper
    return decorator
