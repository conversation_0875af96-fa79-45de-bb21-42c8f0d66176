"""
认证相关数据模式
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, validator
from uuid import UUID

from app.models.user import UserType


class Token(BaseModel):
    """令牌响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    sub: Optional[str] = None
    exp: Optional[datetime] = None


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('用户名不能为空')
        return v.strip()
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v) == 0:
            raise ValueError('密码不能为空')
        return v


class UserRegister(BaseModel):
    """用户注册模式"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    user_type: UserType = UserType.INDIVIDUAL
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('用户名长度至少3位')
        if len(v) > 50:
            raise ValueError('用户名长度不能超过50位')
        if not v.replace('_', '').isalnum():
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v.lower()
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if len(v) > 128:
            raise ValueError('密码长度不能超过128位')
        return v
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if v and len(v) > 100:
            raise ValueError('姓名长度不能超过100位')
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v:
            # 简单的手机号验证
            import re
            if not re.match(r'^1[3-9]\d{9}$', v):
                raise ValueError('手机号格式不正确')
        return v


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str


class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""
    token: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('新密码长度至少8位')
        return v


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求模式"""
    email: EmailStr


class PhoneVerificationRequest(BaseModel):
    """手机验证请求模式"""
    phone: str
    
    @validator('phone')
    def validate_phone(cls, v):
        import re
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v


class PhoneVerificationConfirm(BaseModel):
    """手机验证确认模式"""
    phone: str
    code: str
    
    @validator('code')
    def validate_code(cls, v):
        if not v.isdigit() or len(v) != 6:
            raise ValueError('验证码格式不正确')
        return v


class UserResponse(BaseModel):
    """用户响应模式（认证模块使用）"""
    id: UUID
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None
    user_type: UserType
    status: str
    email_verified: bool = False
    phone_verified: bool = False
    created_at: datetime
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """登录响应模式"""
    user: UserResponse
    token: Token


class RegisterResponse(BaseModel):
    """注册响应模式"""
    user: UserResponse
    message: str = "注册成功"
