"""
文书相关数据模式
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator
from uuid import UUID

from app.models.document import DocumentStatus


class DocumentGenerationRequest(BaseModel):
    """文书生成请求模式"""
    document_type: str
    template_id: Optional[str] = None
    input_data: Dict[str, Any] = {}
    
    @validator('document_type')
    def validate_document_type(cls, v):
        allowed_types = ['complaint', 'contract', 'power_of_attorney', 'agreement', 'notice']
        if v not in allowed_types:
            raise ValueError(f'不支持的文书类型，支持的类型：{", ".join(allowed_types)}')
        return v


class DocumentGenerationResponse(BaseModel):
    """文书生成响应模式"""
    id: UUID
    document_type: str
    template_id: Optional[UUID] = None
    generated_content: Optional[str] = None
    file_path: Optional[str] = None
    status: DocumentStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DocumentTemplate(BaseModel):
    """文书模板模式"""
    id: str
    name: str
    category: str
    description: str
    usage_count: int
    rating: float


class DocumentTemplatesResponse(BaseModel):
    """文书模板响应模式"""
    templates: List[Dict[str, Any]]
    total: int
    page: int
    size: int


class DocumentGenerationSummary(BaseModel):
    """文书生成摘要模式"""
    id: UUID
    document_type: str
    status: DocumentStatus
    created_at: datetime
    
    class Config:
        from_attributes = True


class DocumentGenerationsResponse(BaseModel):
    """文书生成列表响应模式"""
    generations: List[DocumentGenerationSummary]
    total: int
    page: int
    size: int


class DocumentStatistics(BaseModel):
    """文书统计模式"""
    total_documents: int
    type_distribution: Dict[str, int]
    status_distribution: Dict[str, int]
    recent_generations: int
