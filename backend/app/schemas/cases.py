"""
案例相关数据模式
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class CaseBase(BaseModel):
    """案例基础模式"""
    case_id: str
    title: str
    court: str
    case_type: str
    case_category: Optional[str] = None
    judgment_date: Optional[datetime] = None


class CaseSearchResult(CaseBase):
    """案例搜索结果模式"""
    id: str
    case_summary: Optional[str] = None
    keywords: Optional[List[str]] = []
    score: Optional[float] = None
    highlight: Optional[Dict[str, List[str]]] = {}


class CaseSearchResponse(BaseModel):
    """案例搜索响应模式"""
    cases: List[Dict[str, Any]]
    total: int
    page: int
    size: int


class CaseDetailResponse(BaseModel):
    """案例详情响应模式"""
    id: Optional[str] = None
    case_id: str
    title: str
    court: str
    case_type: str
    case_category: Optional[str] = None
    judgment_date: Optional[datetime] = None
    parties: Optional[Dict[str, Any]] = {}
    case_summary: Optional[str] = None
    facts: Optional[str] = None
    court_opinion: Optional[str] = None
    judgment_result: Optional[str] = None
    legal_basis: Optional[List[str]] = []
    keywords: Optional[List[str]] = []
    similar_cases: Optional[List[str]] = []
    full_text: Optional[str] = None
    source_url: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class SimilarCaseResponse(BaseModel):
    """相似案例响应模式"""
    cases: List[Dict[str, Any]]


class CaseStatisticsResponse(BaseModel):
    """案例统计响应模式"""
    total_cases: int
    case_types: Dict[str, int]
    top_courts: Dict[str, int]


class CaseFavoriteRequest(BaseModel):
    """案例收藏请求模式"""
    case_id: str
    case_title: str
