"""
收藏相关数据模式
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator
from uuid import UUID

from app.models.favorite import ItemType


class FavoriteRequest(BaseModel):
    """收藏请求模式"""
    item_type: ItemType
    item_id: str
    item_title: str
    item_data: Optional[Dict[str, Any]] = {}
    
    @validator('item_id')
    def validate_item_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('项目ID不能为空')
        return v.strip()
    
    @validator('item_title')
    def validate_item_title(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('项目标题不能为空')
        if len(v) > 200:
            raise ValueError('项目标题过长')
        return v.strip()


class FavoriteResponse(BaseModel):
    """收藏响应模式"""
    id: UUID
    item_type: ItemType
    item_id: str
    item_title: str
    item_data: Dict[str, Any] = {}
    created_at: datetime
    
    class Config:
        from_attributes = True


class FavoritesListResponse(BaseModel):
    """收藏列表响应模式"""
    favorites: List[FavoriteResponse]
    total: int
    page: int
    size: int


class FavoriteStatisticsResponse(BaseModel):
    """收藏统计响应模式"""
    total_favorites: int
    by_type: Dict[str, int]
