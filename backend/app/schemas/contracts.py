"""
合同相关数据模式
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator
from uuid import UUID

from app.models.contract import RiskLevel, ContractStatus


class ContractAnalysisRequest(BaseModel):
    """合同分析请求模式"""
    contract_name: str
    
    @validator('contract_name')
    def validate_contract_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('合同名称不能为空')
        if len(v) > 200:
            raise ValueError('合同名称过长')
        return v.strip()


class RiskPoint(BaseModel):
    """风险点模式"""
    type: str
    description: str
    severity: str
    location: str


class ContractAnalysisResponse(BaseModel):
    """合同分析响应模式"""
    id: UUID
    contract_name: str
    contract_type: str
    risk_level: RiskLevel
    risk_points: List[Dict[str, Any]] = []
    suggestions: List[str] = []
    status: ContractStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ContractTemplate(BaseModel):
    """合同模板模式"""
    id: str
    name: str
    category: str
    description: str
    usage_count: int
    rating: float


class ContractTemplatesResponse(BaseModel):
    """合同模板响应模式"""
    templates: List[Dict[str, Any]]
    total: int
    page: int
    size: int


class ContractReviewSummary(BaseModel):
    """合同审查摘要模式"""
    id: UUID
    contract_name: str
    contract_type: str
    risk_level: RiskLevel
    status: ContractStatus
    created_at: datetime
    
    class Config:
        from_attributes = True


class ContractReviewsResponse(BaseModel):
    """合同审查列表响应模式"""
    reviews: List[ContractReviewSummary]
    total: int
    page: int
    size: int


class ContractStatistics(BaseModel):
    """合同统计模式"""
    total_contracts: int
    risk_distribution: Dict[str, int]
    type_distribution: Dict[str, int]
    recent_analyses: int
