"""
问答相关数据模式
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator
from uuid import UUID

from app.models.qa import QAStatus


class QARequest(BaseModel):
    """问答请求模式"""
    question: str
    category: Optional[str] = None
    session_id: Optional[str] = None
    
    @validator('question')
    def validate_question(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('问题内容过短，请详细描述您的法律问题')
        if len(v) > 2000:
            raise ValueError('问题内容过长，请控制在2000字以内')
        return v.strip()


class QAResponse(BaseModel):
    """问答响应模式"""
    id: UUID
    question: str
    answer: Optional[str] = None
    category: Optional[str] = None
    confidence_score: Optional[float] = None
    feedback_score: Optional[int] = None
    status: QAStatus
    metadata: Optional[Dict[str, Any]] = {}
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class QAHistoryResponse(BaseModel):
    """问答历史响应模式"""
    records: List[QAResponse]
    total: int
    page: int
    size: int


class QAFeedbackRequest(BaseModel):
    """问答反馈请求模式"""
    feedback_score: int
    feedback_comment: Optional[str] = None
    
    @validator('feedback_score')
    def validate_feedback_score(cls, v):
        if v < 1 or v > 5:
            raise ValueError('评分必须在1-5之间')
        return v


class QAStatistics(BaseModel):
    """问答统计模式"""
    total_questions: int
    answered_questions: int
    average_confidence: float
    average_feedback: float
    popular_categories: List[Dict[str, Any]]


class PopularQuestion(BaseModel):
    """热门问题模式"""
    question: str
    category: str
    ask_count: int


class PopularQuestionsResponse(BaseModel):
    """热门问题响应模式"""
    questions: List[PopularQuestion]
