"""
纠纷解决相关数据模式
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, validator


class DisputeAnalysisRequest(BaseModel):
    """纠纷分析请求模式"""
    dispute_description: str
    dispute_type: Optional[str] = None
    amount_involved: Optional[float] = None
    parties_info: Optional[Dict[str, Any]] = None
    
    @validator('dispute_description')
    def validate_dispute_description(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('请详细描述纠纷情况，至少10个字符')
        if len(v) > 5000:
            raise ValueError('纠纷描述过长，请控制在5000字以内')
        return v.strip()
    
    @validator('amount_involved')
    def validate_amount_involved(cls, v):
        if v is not None and v < 0:
            raise ValueError('涉及金额不能为负数')
        return v


class ResolutionSuggestion(BaseModel):
    """解决建议模式"""
    method: str
    title: str
    description: str
    pros: List[str]
    cons: List[str]
    success_rate: float
    time_estimate: str
    cost_estimate: str


class DisputeAnalysisResponse(BaseModel):
    """纠纷分析响应模式"""
    dispute_type: str
    severity: str
    suggestions: List[ResolutionSuggestion]
    legal_basis: List[str]
    time_estimate: str
    cost_estimate: str
    success_rate: float


class DisputeSolutionsResponse(BaseModel):
    """纠纷解决方案响应模式"""
    solutions: List[ResolutionSuggestion]


class DisputeType(BaseModel):
    """纠纷类型模式"""
    code: str
    name: str
    description: str


class DisputeTypesResponse(BaseModel):
    """纠纷类型响应模式"""
    dispute_types: List[DisputeType]
