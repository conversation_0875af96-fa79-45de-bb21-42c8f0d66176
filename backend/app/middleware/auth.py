"""
认证中间件
"""

import time
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import logging

from app.core.security import verify_token, token_blacklist
from app.core.database import AsyncSessionLocal
from app.services.users import UserService
from app.models.user import UserStatus

logger = logging.getLogger(__name__)


class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        # 不需要认证的路径
        self.exclude_paths = exclude_paths or [
            "/",
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/register",
            "/api/v1/auth/login",
            "/api/v1/auth/login/json",
            "/api/v1/auth/refresh",
            "/api/v1/auth/verify-email",
            "/api/v1/auth/confirm-email",
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        # 检查是否需要认证
        if self._should_skip_auth(request):
            return await call_next(request)
        
        # 获取认证令牌
        token = self._extract_token(request)
        if not token:
            return self._create_auth_error_response("未提供认证令牌")
        
        # 检查令牌黑名单
        if token_blacklist.is_blacklisted(token):
            return self._create_auth_error_response("令牌已失效")
        
        # 验证令牌
        user_id = verify_token(token)
        if not user_id:
            return self._create_auth_error_response("无效的认证令牌")
        
        # 验证用户状态
        try:
            async with AsyncSessionLocal() as db:
                user_service = UserService(db)
                user = await user_service.get_user_by_id(user_id)
                
                if not user:
                    return self._create_auth_error_response("用户不存在")
                
                if user.status != UserStatus.ACTIVE:
                    return self._create_auth_error_response("用户账户已被禁用")
                
                # 将用户信息添加到请求状态
                request.state.current_user = user
                request.state.user_id = str(user.id)
                
        except Exception as e:
            logger.error(f"认证中间件错误: {e}")
            return self._create_auth_error_response("认证服务异常")
        
        return await call_next(request)
    
    def _should_skip_auth(self, request: Request) -> bool:
        """检查是否应该跳过认证"""
        path = request.url.path
        
        # 检查排除路径
        for exclude_path in self.exclude_paths:
            if path.startswith(exclude_path):
                return True
        
        # 检查静态文件
        if path.startswith("/static/"):
            return True
        
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """提取认证令牌"""
        authorization = request.headers.get("Authorization")
        if not authorization:
            return None
        
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                return None
            return token
        except ValueError:
            return None
    
    def _create_auth_error_response(self, message: str) -> JSONResponse:
        """创建认证错误响应"""
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "code": 40101,
                "message": message,
                "timestamp": time.time(),
            }
        )


class PermissionMiddleware(BaseHTTPMiddleware):
    """权限中间件"""
    
    def __init__(self, app, permission_config: Optional[dict] = None):
        super().__init__(app)
        # 权限配置
        self.permission_config = permission_config or {
            "/api/v1/admin/": ["lawyer"],  # 管理功能需要律师权限
            "/api/v1/users/": ["lawyer"],  # 用户管理需要律师权限
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        # 检查是否需要权限验证
        if not hasattr(request.state, "current_user"):
            return await call_next(request)
        
        user = request.state.current_user
        path = request.url.path
        
        # 检查权限配置
        for path_prefix, required_user_types in self.permission_config.items():
            if path.startswith(path_prefix):
                if user.user_type not in required_user_types:
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={
                            "code": 40301,
                            "message": f"需要{'/'.join(required_user_types)}用户权限",
                            "timestamp": time.time(),
                        }
                    )
                break
        
        return await call_next(request)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """添加安全头"""
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # 添加CSP头（开发环境相对宽松）
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' ws: wss:; "
            "frame-ancestors 'none';"
        )
        response.headers["Content-Security-Policy"] = csp
        
        return response


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """请求验证中间件"""
    
    def __init__(self, app, max_request_size: int = 10 * 1024 * 1024):  # 10MB
        super().__init__(app)
        self.max_request_size = max_request_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """验证请求"""
        
        # 检查请求大小
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            return JSONResponse(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                content={
                    "code": 41301,
                    "message": "请求体过大",
                    "timestamp": time.time(),
                }
            )
        
        # 检查Content-Type
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith(("application/json", "multipart/form-data", "application/x-www-form-urlencoded")):
                return JSONResponse(
                    status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                    content={
                        "code": 41501,
                        "message": "不支持的媒体类型",
                        "timestamp": time.time(),
                    }
                )
        
        return await call_next(request)


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """IP白名单中间件"""
    
    def __init__(self, app, whitelist: Optional[list] = None, enabled: bool = False):
        super().__init__(app)
        self.whitelist = whitelist or []
        self.enabled = enabled
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """检查IP白名单"""
        
        if not self.enabled or not self.whitelist:
            return await call_next(request)
        
        client_ip = self._get_client_ip(request)
        
        if client_ip not in self.whitelist:
            logger.warning(f"IP访问被拒绝: {client_ip}")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "code": 40302,
                    "message": "访问被拒绝",
                    "timestamp": time.time(),
                }
            )
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
