"""
请求频率限制中间件
"""

import time
import asyncio
from typing import Callable, Dict, Optional, Tuple
from collections import defaultdict, deque
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """请求频率限制中间件"""
    
    def __init__(
        self,
        app,
        default_requests: int = 100,
        default_window: int = 60,
        path_limits: Optional[Dict[str, Tuple[int, int]]] = None
    ):
        super().__init__(app)
        self.default_requests = default_requests  # 默认请求数
        self.default_window = default_window      # 默认时间窗口（秒）
        
        # 特定路径的限制配置
        self.path_limits = path_limits or {
            "/api/v1/auth/login": (5, 300),      # 登录：5次/5分钟
            "/api/v1/auth/register": (3, 3600),  # 注册：3次/小时
            "/api/v1/qa/": (20, 60),             # 问答：20次/分钟
            "/api/v1/contracts/": (10, 300),     # 合同：10次/5分钟
            "/api/v1/documents/": (5, 300),      # 文书：5次/5分钟
        }
        
        # 存储请求记录
        self.requests = defaultdict(lambda: defaultdict(deque))
        
        # 启动清理任务
        asyncio.create_task(self._cleanup_expired_records())
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        # 获取客户端标识
        client_id = self._get_client_id(request)
        
        # 获取限制配置
        requests_limit, window_seconds = self._get_rate_limit(request)
        
        # 检查频率限制
        if not self._is_allowed(client_id, request.url.path, requests_limit, window_seconds):
            logger.warning(f"请求频率超限: {client_id}, path: {request.url.path}")
            return JSONResponse(
                status_code=429,
                content={
                    "code": 42901,
                    "message": "请求过于频繁，请稍后再试",
                    "retry_after": window_seconds,
                    "timestamp": time.time(),
                },
                headers={"Retry-After": str(window_seconds)}
            )
        
        # 记录请求
        self._record_request(client_id, request.url.path)
        
        response = await call_next(request)
        
        # 添加频率限制头
        remaining = self._get_remaining_requests(client_id, request.url.path, requests_limit, window_seconds)
        response.headers["X-RateLimit-Limit"] = str(requests_limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + window_seconds)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID（如果已认证）
        if hasattr(request.state, "user_id"):
            return f"user:{request.state.user_id}"
        
        # 使用IP地址
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _get_rate_limit(self, request: Request) -> Tuple[int, int]:
        """获取频率限制配置"""
        path = request.url.path
        
        # 检查特定路径配置
        for path_prefix, (requests, window) in self.path_limits.items():
            if path.startswith(path_prefix):
                return requests, window
        
        # 返回默认配置
        return self.default_requests, self.default_window
    
    def _is_allowed(self, client_id: str, path: str, requests_limit: int, window_seconds: int) -> bool:
        """检查是否允许请求"""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        # 获取请求记录
        request_times = self.requests[client_id][path]
        
        # 清理过期记录
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        # 检查是否超过限制
        return len(request_times) < requests_limit
    
    def _record_request(self, client_id: str, path: str) -> None:
        """记录请求"""
        current_time = time.time()
        self.requests[client_id][path].append(current_time)
    
    def _get_remaining_requests(self, client_id: str, path: str, requests_limit: int, window_seconds: int) -> int:
        """获取剩余请求数"""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        request_times = self.requests[client_id][path]
        
        # 清理过期记录
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        return max(0, requests_limit - len(request_times))
    
    async def _cleanup_expired_records(self) -> None:
        """清理过期记录"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                current_time = time.time()
                
                # 清理过期的客户端记录
                expired_clients = []
                for client_id, paths in self.requests.items():
                    expired_paths = []
                    for path, request_times in paths.items():
                        # 清理过期的请求记录
                        while request_times and request_times[0] < current_time - 3600:  # 1小时前的记录
                            request_times.popleft()
                        
                        # 如果路径下没有记录了，标记为过期
                        if not request_times:
                            expired_paths.append(path)
                    
                    # 删除过期路径
                    for path in expired_paths:
                        del paths[path]
                    
                    # 如果客户端下没有路径了，标记为过期
                    if not paths:
                        expired_clients.append(client_id)
                
                # 删除过期客户端
                for client_id in expired_clients:
                    del self.requests[client_id]
                
                if expired_clients or any(expired_paths for expired_paths in []):
                    logger.info(f"清理过期频率限制记录: {len(expired_clients)} 个客户端")
                    
            except Exception as e:
                logger.error(f"清理频率限制记录失败: {e}")


class AdaptiveRateLimitMiddleware(BaseHTTPMiddleware):
    """自适应频率限制中间件"""
    
    def __init__(self, app, base_limit: int = 100, window: int = 60):
        super().__init__(app)
        self.base_limit = base_limit
        self.window = window
        self.requests = defaultdict(lambda: defaultdict(deque))
        self.user_scores = defaultdict(float)  # 用户信誉分数
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        client_id = self._get_client_id(request)
        
        # 根据用户信誉调整限制
        user_score = self.user_scores.get(client_id, 1.0)
        adjusted_limit = int(self.base_limit * user_score)
        
        if not self._is_allowed(client_id, request.url.path, adjusted_limit, self.window):
            # 降低用户信誉分数
            self.user_scores[client_id] = max(0.1, user_score * 0.9)
            
            return JSONResponse(
                status_code=429,
                content={
                    "code": 42901,
                    "message": "请求过于频繁，请稍后再试",
                    "retry_after": self.window,
                    "timestamp": time.time(),
                }
            )
        
        self._record_request(client_id, request.url.path)
        response = await call_next(request)
        
        # 根据响应状态调整用户信誉
        if response.status_code < 400:
            # 成功请求，提高信誉分数
            self.user_scores[client_id] = min(2.0, user_score * 1.01)
        elif response.status_code >= 400:
            # 错误请求，降低信誉分数
            self.user_scores[client_id] = max(0.1, user_score * 0.95)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        if hasattr(request.state, "user_id"):
            return f"user:{request.state.user_id}"
        
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"
    
    def _is_allowed(self, client_id: str, path: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        current_time = time.time()
        cutoff_time = current_time - window
        
        request_times = self.requests[client_id][path]
        
        # 清理过期记录
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        return len(request_times) < limit
    
    def _record_request(self, client_id: str, path: str) -> None:
        """记录请求"""
        current_time = time.time()
        self.requests[client_id][path].append(current_time)
