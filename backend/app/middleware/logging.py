"""
日志中间件
"""

import time
import json
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

from app.core.logging import business_logger

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    def __init__(self, app, log_requests: bool = True, log_responses: bool = False):
        super().__init__(app)
        self.log_requests = log_requests
        self.log_responses = log_responses
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 记录请求日志
        if self.log_requests:
            await self._log_request(request, request_id, client_ip, user_agent)
        
        # 处理请求
        try:
            response = await call_next(request)
        except Exception as e:
            # 记录异常
            process_time = time.time() - start_time
            logger.error(
                f"请求处理异常: {request.method} {request.url.path} - "
                f"请求ID: {request_id} - "
                f"处理时间: {process_time:.4f}s - "
                f"异常: {str(e)}"
            )
            raise
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.4f}"
        
        # 记录响应日志
        await self._log_response(request, response, request_id, process_time, client_ip)
        
        return response
    
    async def _log_request(self, request: Request, request_id: str, client_ip: str, user_agent: str) -> None:
        """记录请求日志"""
        
        # 获取用户ID（如果已认证）
        user_id = getattr(request.state, "user_id", None)
        
        # 构建请求日志
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "client_ip": client_ip,
            "user_agent": user_agent,
            "user_id": user_id,
        }
        
        # 记录请求体（仅对特定方法）
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                # 注意：这里会消耗请求体，需要重新设置
                body = await request.body()
                if body:
                    content_type = request.headers.get("content-type", "")
                    if "application/json" in content_type:
                        try:
                            log_data["body"] = json.loads(body.decode())
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            log_data["body"] = "<无法解析的JSON>"
                    else:
                        log_data["body"] = f"<{content_type}数据>"
            except Exception:
                log_data["body"] = "<无法读取请求体>"
        
        logger.info(f"请求开始: {json.dumps(log_data, ensure_ascii=False, default=str)}")
        
        # 记录业务日志
        if user_id:
            business_logger.log_api_call(
                endpoint=request.url.path,
                method=request.method,
                user_id=user_id
            )
    
    async def _log_response(
        self,
        request: Request,
        response: Response,
        request_id: str,
        process_time: float,
        client_ip: str
    ) -> None:
        """记录响应日志"""
        
        user_id = getattr(request.state, "user_id", None)
        
        # 构建响应日志
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time": process_time,
            "client_ip": client_ip,
            "user_id": user_id,
        }
        
        # 记录响应头
        if self.log_responses:
            log_data["response_headers"] = dict(response.headers)
        
        # 根据状态码选择日志级别
        if response.status_code < 400:
            logger.info(f"请求完成: {json.dumps(log_data, ensure_ascii=False, default=str)}")
        elif response.status_code < 500:
            logger.warning(f"客户端错误: {json.dumps(log_data, ensure_ascii=False, default=str)}")
        else:
            logger.error(f"服务器错误: {json.dumps(log_data, ensure_ascii=False, default=str)}")
        
        # 更新业务日志
        if user_id:
            business_logger.log_api_call(
                endpoint=request.url.path,
                method=request.method,
                user_id=user_id,
                status_code=response.status_code,
                response_time=process_time
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


class AuditLogMiddleware(BaseHTTPMiddleware):
    """审计日志中间件"""
    
    def __init__(self, app, audit_paths: list = None):
        super().__init__(app)
        # 需要审计的路径
        self.audit_paths = audit_paths or [
            "/api/v1/users/",
            "/api/v1/admin/",
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        
        # 检查是否需要审计
        if not self._should_audit(request):
            return await call_next(request)
        
        # 记录审计信息
        await self._log_audit(request)
        
        response = await call_next(request)
        
        # 记录审计结果
        await self._log_audit_result(request, response)
        
        return response
    
    def _should_audit(self, request: Request) -> bool:
        """检查是否需要审计"""
        path = request.url.path
        
        for audit_path in self.audit_paths:
            if path.startswith(audit_path):
                return True
        
        return False
    
    async def _log_audit(self, request: Request) -> None:
        """记录审计日志"""
        user_id = getattr(request.state, "user_id", None)
        
        if not user_id:
            return
        
        audit_data = {
            "user_id": user_id,
            "action": f"{request.method} {request.url.path}",
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", ""),
            "timestamp": time.time(),
        }
        
        # 记录请求参数
        if request.query_params:
            audit_data["query_params"] = dict(request.query_params)
        
        logger.info(f"审计日志: {json.dumps(audit_data, ensure_ascii=False, default=str)}")
    
    async def _log_audit_result(self, request: Request, response: Response) -> None:
        """记录审计结果"""
        user_id = getattr(request.state, "user_id", None)
        
        if not user_id:
            return
        
        result_data = {
            "user_id": user_id,
            "action": f"{request.method} {request.url.path}",
            "status_code": response.status_code,
            "success": response.status_code < 400,
            "timestamp": time.time(),
        }
        
        logger.info(f"审计结果: {json.dumps(result_data, ensure_ascii=False, default=str)}")
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
