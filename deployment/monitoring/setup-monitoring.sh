#!/bin/bash

# AI法律助手监控系统设置脚本

set -e

echo "📊 开始设置监控和日志系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建必要的目录
create_directories() {
    echo "📁 创建监控相关目录..."
    mkdir -p deployment/monitoring/{grafana/provisioning/{datasources,dashboards/json},prometheus/rules}
    mkdir -p logs/{application,nginx,database}
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 启动监控服务
start_monitoring_services() {
    echo "🚀 启动监控服务..."
    
    # 启动Prometheus和Grafana
    docker-compose -f docker-compose.dev.yml up -d prometheus grafana
    
    echo "⏳ 等待监控服务启动..."
    sleep 20
}

# 检查监控服务状态
check_monitoring_services() {
    echo "🔍 检查监控服务状态..."
    
    # 检查Prometheus
    if curl -s http://localhost:9090/-/healthy > /dev/null; then
        echo -e "${GREEN}✅ Prometheus运行正常${NC}"
    else
        echo -e "${RED}❌ Prometheus启动失败${NC}"
        exit 1
    fi
    
    # 检查Grafana
    if curl -s http://localhost:3001/api/health > /dev/null; then
        echo -e "${GREEN}✅ Grafana运行正常${NC}"
    else
        echo -e "${RED}❌ Grafana启动失败${NC}"
        exit 1
    fi
}

# 创建Prometheus告警规则
create_alert_rules() {
    echo "⚠️ 创建告警规则..."
    
    cat > deployment/monitoring/prometheus/rules/legal-assistant.yml << 'EOF'
groups:
  - name: legal-assistant-alerts
    rules:
      # 应用程序告警
      - alert: ApplicationDown
        expr: up{job="legal-assistant-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AI法律助手后端服务宕机"
          description: "后端服务已宕机超过1分钟"

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过高"
          description: "95%的请求响应时间超过2秒"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "错误率过高"
          description: "5xx错误率超过10%"

      # 数据库告警
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL数据库宕机"
          description: "PostgreSQL数据库已宕机超过1分钟"

      - alert: MongoDBDown
        expr: up{job="mongodb"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MongoDB数据库宕机"
          description: "MongoDB数据库已宕机超过1分钟"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis缓存宕机"
          description: "Redis缓存已宕机超过1分钟"

      - alert: ElasticsearchDown
        expr: up{job="elasticsearch"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Elasticsearch搜索引擎宕机"
          description: "Elasticsearch搜索引擎已宕机超过1分钟"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过80%"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率超过80%"
EOF
    
    echo -e "${GREEN}✅ 告警规则创建完成${NC}"
}

# 创建Grafana仪表板
create_grafana_dashboards() {
    echo "📊 创建Grafana仪表板..."
    
    # 应用程序监控仪表板
    cat > deployment/monitoring/grafana/provisioning/dashboards/json/application-dashboard.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "AI法律助手应用监控",
    "tags": ["legal-assistant", "application"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "请求总数",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total[5m]))",
            "legendFormat": "RPS"
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "5s"
  }
}
EOF
    
    # 数据库监控仪表板
    cat > deployment/monitoring/grafana/provisioning/dashboards/json/database-dashboard.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "AI法律助手数据库监控",
    "tags": ["legal-assistant", "database"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "数据库连接状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=~\"postgres|mongodb|redis|elasticsearch\"}",
            "legendFormat": "{{job}}"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "30s"
  }
}
EOF
    
    echo -e "${GREEN}✅ Grafana仪表板创建完成${NC}"
}

# 配置日志收集
setup_logging() {
    echo "📝 配置日志收集..."
    
    # 创建日志配置文件
    cat > deployment/logging/logstash.conf << 'EOF'
input {
  beats {
    port => 5044
  }
  
  file {
    path => "/var/log/legal-assistant/*.log"
    start_position => "beginning"
    codec => "json"
  }
}

filter {
  if [fields][service] == "legal-assistant-backend" {
    mutate {
      add_field => { "service" => "backend" }
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "legal-assistant-logs-%{+YYYY.MM.dd}"
  }
  
  stdout {
    codec => rubydebug
  }
}
EOF
    
    echo -e "${GREEN}✅ 日志配置完成${NC}"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🔗 监控系统访问信息："
    echo "  Prometheus: http://localhost:9090"
    echo "  Grafana: http://localhost:3001"
    echo "    用户名: admin"
    echo "    密码: admin123"
    echo ""
    echo "📊 已创建的仪表板："
    echo "  - AI法律助手应用监控"
    echo "  - AI法律助手数据库监控"
    echo ""
    echo "⚠️ 告警规则："
    echo "  - 应用程序宕机告警"
    echo "  - 响应时间过高告警"
    echo "  - 错误率过高告警"
    echo "  - 数据库宕机告警"
    echo "  - 系统资源告警"
}

# 主函数
main() {
    echo "🎯 AI法律助手监控系统设置"
    echo "============================"
    
    create_directories
    create_alert_rules
    create_grafana_dashboards
    setup_logging
    start_monitoring_services
    check_monitoring_services
    show_access_info
    
    echo ""
    echo -e "${GREEN}🎉 监控系统设置完成！${NC}"
    echo ""
    echo "📝 下一步："
    echo "  1. 访问Grafana配置仪表板"
    echo "  2. 根据需要调整告警规则"
    echo "  3. 配置告警通知渠道"
}

# 错误处理
trap 'echo -e "${RED}❌ 脚本执行失败${NC}"; exit 1' ERR

# 执行主函数
main "$@"
