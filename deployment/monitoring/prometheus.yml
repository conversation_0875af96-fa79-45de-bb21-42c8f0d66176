# AI法律助手Prometheus监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'legal-assistant-monitor'

# 规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # 应用程序监控
  - job_name: 'legal-assistant-backend'
    static_configs:
      - targets: ['host.docker.internal:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s

  # 数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    scrape_interval: 30s
    metrics_path: /_prometheus/metrics

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Docker监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
