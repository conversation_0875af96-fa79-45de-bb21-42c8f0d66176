// AI法律助手MongoDB数据库初始化脚本

// 切换到legal_db数据库
db = db.getSiblingDB('legal_db');

// 创建用户
db.createUser({
  user: 'legal_user',
  pwd: 'legal_password',
  roles: [
    {
      role: 'readWrite',
      db: 'legal_db'
    }
  ]
});

// 创建法律案例集合
db.createCollection('legal_cases', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['case_id', 'title', 'court', 'case_type'],
      properties: {
        case_id: {
          bsonType: 'string',
          description: '案例唯一标识'
        },
        title: {
          bsonType: 'string',
          description: '案例标题'
        },
        court: {
          bsonType: 'string',
          description: '审理法院'
        },
        case_type: {
          bsonType: 'string',
          description: '案件类型'
        },
        case_category: {
          bsonType: 'string',
          description: '案件分类'
        },
        judgment_date: {
          bsonType: 'date',
          description: '判决日期'
        },
        parties: {
          bsonType: 'object',
          properties: {
            plaintiff: {
              bsonType: 'array',
              items: { bsonType: 'string' }
            },
            defendant: {
              bsonType: 'array',
              items: { bsonType: 'string' }
            },
            third_party: {
              bsonType: 'array',
              items: { bsonType: 'string' }
            }
          }
        },
        case_summary: {
          bsonType: 'string',
          description: '案例摘要'
        },
        facts: {
          bsonType: 'string',
          description: '案件事实'
        },
        court_opinion: {
          bsonType: 'string',
          description: '法院观点'
        },
        judgment_result: {
          bsonType: 'string',
          description: '判决结果'
        },
        legal_basis: {
          bsonType: 'array',
          items: { bsonType: 'string' },
          description: '法律依据'
        },
        keywords: {
          bsonType: 'array',
          items: { bsonType: 'string' },
          description: '关键词'
        },
        similar_cases: {
          bsonType: 'array',
          items: { bsonType: 'string' },
          description: '相似案例ID'
        },
        full_text: {
          bsonType: 'string',
          description: '完整文本'
        },
        source_url: {
          bsonType: 'string',
          description: '数据源URL'
        },
        created_at: {
          bsonType: 'date',
          description: '创建时间'
        },
        updated_at: {
          bsonType: 'date',
          description: '更新时间'
        },
        indexed_at: {
          bsonType: 'date',
          description: '索引时间'
        }
      }
    }
  }
});

// 创建法律法规集合
db.createCollection('legal_regulations', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['regulation_id', 'title', 'type', 'level'],
      properties: {
        regulation_id: {
          bsonType: 'string',
          description: '法规唯一标识'
        },
        title: {
          bsonType: 'string',
          description: '法规标题'
        },
        type: {
          bsonType: 'string',
          enum: ['law', 'regulation', 'rule', 'interpretation'],
          description: '法规类型'
        },
        level: {
          bsonType: 'string',
          enum: ['national', 'provincial', 'municipal'],
          description: '法规层级'
        },
        issuing_authority: {
          bsonType: 'string',
          description: '发布机关'
        },
        issue_date: {
          bsonType: 'date',
          description: '发布日期'
        },
        effective_date: {
          bsonType: 'date',
          description: '生效日期'
        },
        status: {
          bsonType: 'string',
          enum: ['effective', 'amended', 'repealed'],
          description: '状态'
        },
        chapters: {
          bsonType: 'array',
          items: {
            bsonType: 'object',
            properties: {
              chapter_number: { bsonType: 'string' },
              chapter_title: { bsonType: 'string' },
              articles: {
                bsonType: 'array',
                items: {
                  bsonType: 'object',
                  properties: {
                    article_number: { bsonType: 'string' },
                    article_content: { bsonType: 'string' },
                    annotations: {
                      bsonType: 'array',
                      items: { bsonType: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        full_text: {
          bsonType: 'string',
          description: '完整文本'
        },
        keywords: {
          bsonType: 'array',
          items: { bsonType: 'string' }
        },
        related_regulations: {
          bsonType: 'array',
          items: { bsonType: 'string' }
        }
      }
    }
  }
});

// 创建合同模板集合
db.createCollection('contract_templates', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['template_id', 'name', 'category'],
      properties: {
        template_id: {
          bsonType: 'string',
          description: '模板唯一标识'
        },
        name: {
          bsonType: 'string',
          description: '模板名称'
        },
        category: {
          bsonType: 'string',
          description: '合同类别'
        },
        description: {
          bsonType: 'string',
          description: '模板描述'
        },
        template_content: {
          bsonType: 'string',
          description: '模板内容'
        },
        variables: {
          bsonType: 'array',
          items: {
            bsonType: 'object',
            properties: {
              name: { bsonType: 'string' },
              type: { bsonType: 'string' },
              required: { bsonType: 'bool' },
              description: { bsonType: 'string' },
              default_value: { bsonType: 'string' }
            }
          }
        },
        risk_clauses: {
          bsonType: 'array',
          items: {
            bsonType: 'object',
            properties: {
              clause_text: { bsonType: 'string' },
              risk_level: { bsonType: 'string' },
              risk_description: { bsonType: 'string' },
              suggestions: {
                bsonType: 'array',
                items: { bsonType: 'string' }
              }
            }
          }
        },
        usage_count: {
          bsonType: 'int',
          description: '使用次数'
        },
        rating: {
          bsonType: 'double',
          description: '用户评分'
        },
        tags: {
          bsonType: 'array',
          items: { bsonType: 'string' }
        },
        created_by: {
          bsonType: 'string',
          description: '创建者'
        }
      }
    }
  }
});

// 创建知识图谱集合
db.createCollection('knowledge_graph', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['entity_id', 'entity_type', 'entity_name'],
      properties: {
        entity_id: {
          bsonType: 'string',
          description: '实体唯一标识'
        },
        entity_type: {
          bsonType: 'string',
          enum: ['concept', 'law', 'case', 'person', 'organization'],
          description: '实体类型'
        },
        entity_name: {
          bsonType: 'string',
          description: '实体名称'
        },
        properties: {
          bsonType: 'object',
          description: '实体属性'
        },
        relationships: {
          bsonType: 'array',
          items: {
            bsonType: 'object',
            properties: {
              relation_type: { bsonType: 'string' },
              target_entity_id: { bsonType: 'string' },
              weight: { bsonType: 'double' }
            }
          }
        }
      }
    }
  }
});

// 创建索引
print('创建索引...');

// 法律案例索引
db.legal_cases.createIndex({ 'case_id': 1 }, { unique: true });
db.legal_cases.createIndex({ 'title': 'text', 'case_summary': 'text', 'full_text': 'text' });
db.legal_cases.createIndex({ 'court': 1 });
db.legal_cases.createIndex({ 'case_type': 1 });
db.legal_cases.createIndex({ 'case_category': 1 });
db.legal_cases.createIndex({ 'judgment_date': -1 });
db.legal_cases.createIndex({ 'keywords': 1 });
db.legal_cases.createIndex({ 'created_at': -1 });

// 法律法规索引
db.legal_regulations.createIndex({ 'regulation_id': 1 }, { unique: true });
db.legal_regulations.createIndex({ 'title': 'text', 'full_text': 'text' });
db.legal_regulations.createIndex({ 'type': 1 });
db.legal_regulations.createIndex({ 'level': 1 });
db.legal_regulations.createIndex({ 'status': 1 });
db.legal_regulations.createIndex({ 'effective_date': -1 });
db.legal_regulations.createIndex({ 'keywords': 1 });

// 合同模板索引
db.contract_templates.createIndex({ 'template_id': 1 }, { unique: true });
db.contract_templates.createIndex({ 'name': 'text', 'description': 'text' });
db.contract_templates.createIndex({ 'category': 1 });
db.contract_templates.createIndex({ 'tags': 1 });
db.contract_templates.createIndex({ 'usage_count': -1 });
db.contract_templates.createIndex({ 'rating': -1 });
db.contract_templates.createIndex({ 'created_at': -1 });

// 知识图谱索引
db.knowledge_graph.createIndex({ 'entity_id': 1 }, { unique: true });
db.knowledge_graph.createIndex({ 'entity_type': 1 });
db.knowledge_graph.createIndex({ 'entity_name': 'text' });
db.knowledge_graph.createIndex({ 'relationships.target_entity_id': 1 });

// 插入示例数据
print('插入示例数据...');

// 示例法律案例
db.legal_cases.insertOne({
  case_id: 'demo_case_001',
  title: '某公司劳动合同纠纷案',
  court: '北京市朝阳区人民法院',
  case_type: '劳动争议',
  case_category: '劳动合同纠纷',
  judgment_date: new Date('2023-06-15'),
  parties: {
    plaintiff: ['张某'],
    defendant: ['某科技有限公司'],
    third_party: []
  },
  case_summary: '员工因公司违法解除劳动合同而提起诉讼',
  facts: '张某于2020年入职某科技公司，2023年公司以业绩不佳为由解除劳动合同...',
  court_opinion: '公司解除劳动合同程序不当，应支付经济补偿金',
  judgment_result: '支持原告诉讼请求，被告支付经济补偿金5万元',
  legal_basis: ['劳动合同法第四十七条', '劳动合同法第八十七条'],
  keywords: ['劳动合同', '违法解除', '经济补偿金'],
  similar_cases: [],
  full_text: '完整的案例文本内容...',
  source_url: 'https://example.com/case/001',
  created_at: new Date(),
  updated_at: new Date(),
  indexed_at: new Date()
});

// 示例合同模板
db.contract_templates.insertOne({
  template_id: 'template_001',
  name: '劳动合同模板',
  category: '劳动合同',
  description: '标准劳动合同模板，适用于一般企业员工',
  template_content: '劳动合同\n\n甲方：{{company_name}}\n乙方：{{employee_name}}\n...',
  variables: [
    {
      name: 'company_name',
      type: 'string',
      required: true,
      description: '公司名称',
      default_value: ''
    },
    {
      name: 'employee_name',
      type: 'string',
      required: true,
      description: '员工姓名',
      default_value: ''
    }
  ],
  risk_clauses: [
    {
      clause_text: '试用期条款',
      risk_level: 'medium',
      risk_description: '试用期长度需符合法律规定',
      suggestions: ['试用期不得超过6个月', '试用期工资不得低于正式工资的80%']
    }
  ],
  usage_count: 0,
  rating: 0.0,
  tags: ['劳动合同', '标准模板'],
  created_by: 'system',
  created_at: new Date(),
  updated_at: new Date()
});

print('MongoDB数据库初始化完成');
