#!/bin/bash

# Elasticsearch插件安装脚本

set -e

echo "🔌 开始安装Elasticsearch插件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 等待Elasticsearch启动
wait_for_elasticsearch() {
    echo "⏳ 等待Elasticsearch启动..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Elasticsearch已启动${NC}"
            return 0
        fi
        
        echo "尝试 $attempt/$max_attempts - 等待Elasticsearch启动..."
        sleep 10
        ((attempt++))
    done
    
    echo -e "${RED}❌ Elasticsearch启动超时${NC}"
    exit 1
}

# 安装IK中文分词器
install_ik_analyzer() {
    echo "📦 安装IK中文分词器..."
    
    # 检查是否已安装
    if docker exec legal-assistant-elasticsearch elasticsearch-plugin list | grep -q "analysis-ik"; then
        echo -e "${YELLOW}⚠️ IK分词器已安装，跳过${NC}"
        return 0
    fi
    
    # 安装IK分词器
    docker exec legal-assistant-elasticsearch elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v8.11.0/elasticsearch-analysis-ik-8.11.0.zip
    
    echo -e "${GREEN}✅ IK中文分词器安装完成${NC}"
}

# 安装拼音分析器
install_pinyin_analyzer() {
    echo "📦 安装拼音分析器..."
    
    # 检查是否已安装
    if docker exec legal-assistant-elasticsearch elasticsearch-plugin list | grep -q "analysis-pinyin"; then
        echo -e "${YELLOW}⚠️ 拼音分析器已安装，跳过${NC}"
        return 0
    fi
    
    # 安装拼音分析器
    docker exec legal-assistant-elasticsearch elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-pinyin/releases/download/v8.11.0/elasticsearch-analysis-pinyin-8.11.0.zip
    
    echo -e "${GREEN}✅ 拼音分析器安装完成${NC}"
}

# 重启Elasticsearch
restart_elasticsearch() {
    echo "🔄 重启Elasticsearch以加载插件..."
    
    docker restart legal-assistant-elasticsearch
    
    # 等待重启完成
    wait_for_elasticsearch
}

# 创建自定义分析器
create_custom_analyzers() {
    echo "⚙️ 创建自定义分析器..."
    
    # 创建法律文本分析器设置
    curl -X PUT "localhost:9200/_template/legal_text_template" \
    -H "Content-Type: application/json" \
    -d '{
        "index_patterns": ["legal_*"],
        "settings": {
            "analysis": {
                "analyzer": {
                    "legal_text_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": [
                            "lowercase",
                            "stop_filter",
                            "synonym_filter"
                        ]
                    },
                    "legal_search_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_smart",
                        "filter": [
                            "lowercase",
                            "stop_filter",
                            "synonym_filter"
                        ]
                    },
                    "pinyin_analyzer": {
                        "type": "custom",
                        "tokenizer": "pinyin_tokenizer",
                        "filter": [
                            "lowercase",
                            "unique"
                        ]
                    }
                },
                "tokenizer": {
                    "pinyin_tokenizer": {
                        "type": "pinyin",
                        "keep_first_letter": true,
                        "keep_separate_first_letter": false,
                        "keep_full_pinyin": true,
                        "keep_joined_full_pinyin": true,
                        "keep_none_chinese": true,
                        "keep_none_chinese_together": true,
                        "keep_none_chinese_in_first_letter": true,
                        "keep_none_chinese_in_joined_full_pinyin": true,
                        "none_chinese_pinyin_tokenize": false,
                        "keep_original": false,
                        "lowercase": true,
                        "trim_whitespace": true,
                        "remove_duplicated_term": true
                    }
                },
                "filter": {
                    "stop_filter": {
                        "type": "stop",
                        "stopwords": ["的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这"]
                    },
                    "synonym_filter": {
                        "type": "synonym",
                        "synonyms": [
                            "法院,人民法院,审判机关",
                            "律师,法律顾问,辩护人",
                            "合同,协议,契约",
                            "起诉,诉讼,告状",
                            "判决,裁决,裁判",
                            "赔偿,补偿,损害赔偿"
                        ]
                    }
                }
            }
        }
    }'
    
    echo -e "${GREEN}✅ 自定义分析器创建完成${NC}"
}

# 测试分析器
test_analyzers() {
    echo "🧪 测试分析器..."
    
    # 测试IK分词器
    echo "测试IK分词器："
    curl -X POST "localhost:9200/_analyze" \
    -H "Content-Type: application/json" \
    -d '{
        "analyzer": "ik_max_word",
        "text": "中华人民共和国劳动合同法"
    }' | jq '.tokens[].token' | head -5
    
    # 测试拼音分析器
    echo "测试拼音分析器："
    curl -X POST "localhost:9200/_analyze" \
    -H "Content-Type: application/json" \
    -d '{
        "analyzer": "pinyin_analyzer",
        "text": "劳动合同"
    }' | jq '.tokens[].token' | head -5
    
    echo -e "${GREEN}✅ 分析器测试完成${NC}"
}

# 创建索引映射
create_index_mappings() {
    echo "📋 创建索引映射..."
    
    # 创建法律案例索引
    curl -X PUT "localhost:9200/legal_cases" \
    -H "Content-Type: application/json" \
    -d '{
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 0,
            "analysis": {
                "analyzer": {
                    "legal_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "stop"]
                    }
                }
            }
        },
        "mappings": {
            "properties": {
                "case_id": {
                    "type": "keyword"
                },
                "title": {
                    "type": "text",
                    "analyzer": "legal_analyzer",
                    "search_analyzer": "ik_smart",
                    "fields": {
                        "pinyin": {
                            "type": "text",
                            "analyzer": "pinyin_analyzer"
                        }
                    }
                },
                "court": {
                    "type": "keyword"
                },
                "case_type": {
                    "type": "keyword"
                },
                "case_category": {
                    "type": "keyword"
                },
                "judgment_date": {
                    "type": "date"
                },
                "case_summary": {
                    "type": "text",
                    "analyzer": "legal_analyzer"
                },
                "facts": {
                    "type": "text",
                    "analyzer": "legal_analyzer"
                },
                "court_opinion": {
                    "type": "text",
                    "analyzer": "legal_analyzer"
                },
                "legal_basis": {
                    "type": "keyword"
                },
                "keywords": {
                    "type": "keyword"
                },
                "full_text": {
                    "type": "text",
                    "analyzer": "legal_analyzer"
                }
            }
        }
    }'
    
    echo -e "${GREEN}✅ 法律案例索引创建完成${NC}"
    
    # 创建法律法规索引
    curl -X PUT "localhost:9200/legal_regulations" \
    -H "Content-Type: application/json" \
    -d '{
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 0
        },
        "mappings": {
            "properties": {
                "regulation_id": {
                    "type": "keyword"
                },
                "title": {
                    "type": "text",
                    "analyzer": "legal_analyzer",
                    "fields": {
                        "pinyin": {
                            "type": "text",
                            "analyzer": "pinyin_analyzer"
                        }
                    }
                },
                "type": {
                    "type": "keyword"
                },
                "level": {
                    "type": "keyword"
                },
                "status": {
                    "type": "keyword"
                },
                "effective_date": {
                    "type": "date"
                },
                "full_text": {
                    "type": "text",
                    "analyzer": "legal_analyzer"
                },
                "keywords": {
                    "type": "keyword"
                }
            }
        }
    }'
    
    echo -e "${GREEN}✅ 法律法规索引创建完成${NC}"
}

# 主函数
main() {
    echo "🎯 Elasticsearch插件安装和配置"
    echo "================================="
    
    wait_for_elasticsearch
    install_ik_analyzer
    install_pinyin_analyzer
    restart_elasticsearch
    create_custom_analyzers
    test_analyzers
    create_index_mappings
    
    echo ""
    echo -e "${GREEN}🎉 Elasticsearch配置完成！${NC}"
    echo ""
    echo "📝 已安装的插件："
    echo "  - IK中文分词器"
    echo "  - 拼音分析器"
    echo ""
    echo "📋 已创建的索引："
    echo "  - legal_cases (法律案例)"
    echo "  - legal_regulations (法律法规)"
    echo ""
    echo "🔗 访问地址："
    echo "  - Elasticsearch: http://localhost:9200"
    echo "  - Kibana: http://localhost:5601"
}

# 错误处理
trap 'echo -e "${RED}❌ 脚本执行失败${NC}"; exit 1' ERR

# 执行主函数
main "$@"
