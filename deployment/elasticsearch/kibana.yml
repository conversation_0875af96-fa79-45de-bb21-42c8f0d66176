# AI法律助手Kibana配置文件

# 服务器配置
server.name: "legal-assistant-kibana"
server.host: "0.0.0.0"
server.port: 5601

# Elasticsearch配置
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: ""
elasticsearch.password: ""

# 监控配置
monitoring.ui.container.elasticsearch.enabled: true

# 安全配置
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "legal-assistant-kibana-encryption-key-32-chars"

# 日志配置
logging.appenders.file.type: file
logging.appenders.file.fileName: /usr/share/kibana/logs/kibana.log
logging.appenders.file.layout.type: json

logging.root.level: info
logging.root.appenders: [default, file]

# 国际化配置
i18n.locale: "zh-CN"

# 数据视图配置
data.search.aggs.shardDelay.enabled: true

# 保存对象配置
savedObjects.maxImportPayloadBytes: 26214400

# 地图配置
map.includeElasticMapsService: true

# 遥测配置
telemetry.enabled: false
telemetry.optIn: false

# 新功能配置
newsfeed.enabled: false

# 安全策略
csp.rules:
  - "script-src 'self' 'unsafe-eval'"
  - "worker-src blob:"
  - "child-src blob:"

# 服务器配置
server.maxPayload: 1048576
server.compression.enabled: true

# 开发模式配置
environment: "development"
