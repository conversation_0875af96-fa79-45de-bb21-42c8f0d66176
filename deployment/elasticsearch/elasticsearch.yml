# AI法律助手Elasticsearch配置文件

# 集群配置
cluster.name: legal-assistant-cluster
node.name: legal-assistant-node-1

# 网络配置
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# 发现配置
discovery.type: single-node

# 路径配置
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# 内存配置
bootstrap.memory_lock: true

# 安全配置
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# 监控配置
xpack.monitoring.collection.enabled: true

# 索引配置
action.auto_create_index: true
action.destructive_requires_name: true

# 搜索配置
search.max_buckets: 65536
indices.query.bool.max_clause_count: 10000

# 中文分析器配置
index.analysis.analyzer.default.type: ik_max_word
index.analysis.search_analyzer.default.type: ik_smart

# 线程池配置
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000
thread_pool.write.size: 4
thread_pool.write.queue_size: 1000

# 缓存配置
indices.fielddata.cache.size: 40%
indices.requests.cache.size: 2%

# 日志配置
logger.org.elasticsearch.discovery: DEBUG
logger.org.elasticsearch.cluster.service: DEBUG

# 慢查询日志
index.search.slowlog.threshold.query.warn: 10s
index.search.slowlog.threshold.query.info: 5s
index.search.slowlog.threshold.query.debug: 2s
index.search.slowlog.threshold.query.trace: 500ms

index.search.slowlog.threshold.fetch.warn: 1s
index.search.slowlog.threshold.fetch.info: 800ms
index.search.slowlog.threshold.fetch.debug: 500ms
index.search.slowlog.threshold.fetch.trace: 200ms

index.indexing.slowlog.threshold.index.warn: 10s
index.indexing.slowlog.threshold.index.info: 5s
index.indexing.slowlog.threshold.index.debug: 2s
index.indexing.slowlog.threshold.index.trace: 500ms

# 索引模板配置
index.number_of_shards: 1
index.number_of_replicas: 0
index.refresh_interval: 1s

# 映射配置
index.mapping.total_fields.limit: 2000
index.mapping.depth.limit: 20
index.mapping.nested_fields.limit: 100

# 熔断器配置
indices.breaker.total.limit: 70%
indices.breaker.fielddata.limit: 40%
indices.breaker.request.limit: 40%

# 恢复配置
cluster.routing.allocation.node_concurrent_recoveries: 2
cluster.routing.allocation.node_initial_primaries_recoveries: 4
indices.recovery.max_bytes_per_sec: 40mb

# 分片配置
cluster.routing.allocation.total_shards_per_node: 1000
cluster.max_shards_per_node: 1000
