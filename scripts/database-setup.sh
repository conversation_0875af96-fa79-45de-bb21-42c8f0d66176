#!/bin/bash

# AI法律助手数据库设置脚本

set -e

echo "🗄️ 开始设置AI法律助手数据库..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker运行正常${NC}"
}

# 检查Docker Compose是否可用
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker Compose可用${NC}"
}

# 创建必要的目录
create_directories() {
    echo "📁 创建必要的目录..."
    mkdir -p deployment/database/{postgres,mongodb,redis}
    mkdir -p data/{postgres,mongodb,redis,elasticsearch}
    mkdir -p logs
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 启动数据库服务
start_databases() {
    echo "🚀 启动数据库服务..."
    
    # 停止可能存在的旧容器
    docker-compose -f docker-compose.dev.yml down
    
    # 启动数据库服务
    docker-compose -f docker-compose.dev.yml up -d postgres mongodb redis
    
    echo "⏳ 等待数据库启动..."
    sleep 15
}

# 检查数据库连接
check_database_connections() {
    echo "🔍 检查数据库连接..."
    
    # 检查PostgreSQL
    echo "检查PostgreSQL连接..."
    if docker exec legal-assistant-postgres pg_isready -U legal_user -d legal_assistant; then
        echo -e "${GREEN}✅ PostgreSQL连接正常${NC}"
    else
        echo -e "${RED}❌ PostgreSQL连接失败${NC}"
        exit 1
    fi
    
    # 检查MongoDB
    echo "检查MongoDB连接..."
    if docker exec legal-assistant-mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ MongoDB连接正常${NC}"
    else
        echo -e "${RED}❌ MongoDB连接失败${NC}"
        exit 1
    fi
    
    # 检查Redis
    echo "检查Redis连接..."
    if docker exec legal-assistant-redis redis-cli ping | grep -q PONG; then
        echo -e "${GREEN}✅ Redis连接正常${NC}"
    else
        echo -e "${RED}❌ Redis连接失败${NC}"
        exit 1
    fi
}

# 初始化数据库数据
initialize_data() {
    echo "📊 初始化数据库数据..."
    
    # PostgreSQL已通过init.sql自动初始化
    echo -e "${GREEN}✅ PostgreSQL数据初始化完成${NC}"
    
    # MongoDB已通过init.js自动初始化
    echo -e "${GREEN}✅ MongoDB数据初始化完成${NC}"
    
    # Redis不需要特殊初始化
    echo -e "${GREEN}✅ Redis初始化完成${NC}"
}

# 启动管理工具
start_admin_tools() {
    echo "🛠️ 启动数据库管理工具..."
    
    docker-compose -f docker-compose.dev.yml up -d pgadmin mongo-express redis-commander
    
    echo -e "${GREEN}✅ 管理工具启动完成${NC}"
    echo ""
    echo "📱 管理工具访问地址："
    echo "  - PgAdmin (PostgreSQL): http://localhost:5050"
    echo "    用户名: <EMAIL>"
    echo "    密码: admin123"
    echo ""
    echo "  - Mongo Express (MongoDB): http://localhost:8081"
    echo "    用户名: admin"
    echo "    密码: admin123"
    echo ""
    echo "  - Redis Commander (Redis): http://localhost:8082"
    echo "    用户名: admin"
    echo "    密码: admin123"
}

# 显示连接信息
show_connection_info() {
    echo ""
    echo "🔗 数据库连接信息："
    echo "  PostgreSQL:"
    echo "    Host: localhost"
    echo "    Port: 5432"
    echo "    Database: legal_assistant"
    echo "    Username: legal_user"
    echo "    Password: legal_password"
    echo ""
    echo "  MongoDB:"
    echo "    Host: localhost"
    echo "    Port: 27017"
    echo "    Database: legal_db"
    echo "    Username: legal_user"
    echo "    Password: legal_password"
    echo ""
    echo "  Redis:"
    echo "    Host: localhost"
    echo "    Port: 6379"
    echo "    Password: (无)"
}

# 创建备份脚本
create_backup_script() {
    echo "💾 创建数据库备份脚本..."
    
    cat > scripts/backup-databases.sh << 'EOF'
#!/bin/bash

# 数据库备份脚本

BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "开始备份数据库..."

# 备份PostgreSQL
echo "备份PostgreSQL..."
docker exec legal-assistant-postgres pg_dump -U legal_user legal_assistant > "$BACKUP_DIR/postgres_backup.sql"

# 备份MongoDB
echo "备份MongoDB..."
docker exec legal-assistant-mongodb mongodump --db legal_db --out /tmp/backup
docker cp legal-assistant-mongodb:/tmp/backup "$BACKUP_DIR/mongodb_backup"

# 备份Redis
echo "备份Redis..."
docker exec legal-assistant-redis redis-cli BGSAVE
docker cp legal-assistant-redis:/data/dump.rdb "$BACKUP_DIR/redis_backup.rdb"

echo "备份完成: $BACKUP_DIR"
EOF
    
    chmod +x scripts/backup-databases.sh
    echo -e "${GREEN}✅ 备份脚本创建完成${NC}"
}

# 主函数
main() {
    echo "🎯 AI法律助手数据库设置"
    echo "=========================="
    
    check_docker
    check_docker_compose
    create_directories
    start_databases
    check_database_connections
    initialize_data
    start_admin_tools
    create_backup_script
    show_connection_info
    
    echo ""
    echo -e "${GREEN}🎉 数据库设置完成！${NC}"
    echo ""
    echo "📝 下一步："
    echo "  1. 访问管理工具检查数据库状态"
    echo "  2. 运行应用程序进行测试"
    echo "  3. 使用 ./scripts/backup-databases.sh 进行数据备份"
    echo ""
    echo "🛑 停止数据库服务："
    echo "  docker-compose -f docker-compose.dev.yml down"
}

# 错误处理
trap 'echo -e "${RED}❌ 脚本执行失败${NC}"; exit 1' ERR

# 执行主函数
main "$@"
