#!/bin/bash

# AI法律助手生产环境部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 示例: ./deploy.sh production v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

log_info "开始部署AI法律助手应用"
log_info "环境: $ENVIRONMENT"
log_info "版本: $VERSION"

# 检查必要的工具
check_requirements() {
    log_info "检查部署环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    required_vars=(
        "DB_PASSWORD"
        "MONGO_PASSWORD"
        "REDIS_PASSWORD"
        "JWT_SECRET_KEY"
        "SMTP_SERVER"
        "SMTP_USERNAME"
        "SMTP_PASSWORD"
        "GRAFANA_PASSWORD"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少以下环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "请设置这些环境变量后重新运行部署脚本"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 备份数据
backup_data() {
    log_info "备份现有数据..."
    
    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份PostgreSQL数据
    if docker ps | grep -q ai-legal-postgres; then
        log_info "备份PostgreSQL数据..."
        docker exec ai-legal-postgres pg_dump -U ai_legal_user ai_legal_db > "$BACKUP_DIR/postgres_backup.sql"
    fi
    
    # 备份MongoDB数据
    if docker ps | grep -q ai-legal-mongodb; then
        log_info "备份MongoDB数据..."
        docker exec ai-legal-mongodb mongodump --db ai_legal_db --out /tmp/backup
        docker cp ai-legal-mongodb:/tmp/backup "$BACKUP_DIR/mongodb_backup"
    fi
    
    # 备份上传文件
    if [[ -d "./uploads" ]]; then
        log_info "备份上传文件..."
        cp -r ./uploads "$BACKUP_DIR/"
    fi
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t ai-legal-backend:$VERSION -f backend/Dockerfile.prod backend/
    
    # 构建前端镜像（如果需要）
    if [[ -f "frontend/web/Dockerfile.prod" ]]; then
        log_info "构建前端镜像..."
        docker build -t ai-legal-frontend:$VERSION -f frontend/web/Dockerfile.prod frontend/web/
    fi
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    # 停止现有服务
    if [[ -f "docker-compose.prod.yml" ]]; then
        log_info "停止现有服务..."
        docker-compose -f docker-compose.prod.yml down
    fi
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查后端服务
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        log_success "后端服务健康检查通过"
    else
        log_error "后端服务健康检查失败"
        return 1
    fi
    
    # 检查前端服务
    if curl -f http://localhost > /dev/null 2>&1; then
        log_success "前端服务健康检查通过"
    else
        log_error "前端服务健康检查失败"
        return 1
    fi
    
    # 检查数据库连接
    if docker exec ai-legal-backend python -c "from app.core.database import engine; print('Database OK')" > /dev/null 2>&1; then
        log_success "数据库连接检查通过"
    else
        log_error "数据库连接检查失败"
        return 1
    fi
    
    log_success "所有健康检查通过"
}

# 数据库迁移
run_migrations() {
    log_info "执行数据库迁移..."
    
    docker exec ai-legal-backend alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的容器
    docker container prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    log_success "清理完成"
}

# 发送通知
send_notification() {
    log_info "发送部署通知..."
    
    # 这里可以集成钉钉、企业微信等通知
    # curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
    #      -H 'Content-Type: application/json' \
    #      -d '{"msgtype": "text","text": {"content": "AI法律助手部署完成"}}'
    
    log_success "部署通知已发送"
}

# 主部署流程
main() {
    log_info "========================================="
    log_info "AI法律助手部署脚本 v1.0.0"
    log_info "========================================="
    
    check_requirements
    check_env_vars
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        backup_data
    fi
    
    build_images
    deploy_services
    run_migrations
    health_check
    
    if [[ $? -eq 0 ]]; then
        cleanup
        send_notification
        log_success "========================================="
        log_success "部署成功完成！"
        log_success "应用访问地址: https://ai-legal-assistant.com"
        log_success "监控面板: http://localhost:3000"
        log_success "========================================="
    else
        log_error "部署失败，请检查日志"
        exit 1
    fi
}

# 捕获中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主流程
main "$@"
