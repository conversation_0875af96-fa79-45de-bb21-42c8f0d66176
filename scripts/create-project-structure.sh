#!/bin/bash

# AI法律助手项目结构创建脚本

set -e

echo "📁 创建AI法律助手项目结构..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建后端目录结构
create_backend_structure() {
    echo "🐍 创建后端目录结构..."
    
    # 核心模块目录
    mkdir -p backend/app/{core,api/v1/endpoints,models,schemas,services,utils,tests}
    
    # 服务模块目录
    mkdir -p backend/app/services/{auth,users,qa,cases,contracts,documents,disputes}
    
    # 数据库模块目录
    mkdir -p backend/app/db/{base,models,repositories}
    
    # 中间件目录
    mkdir -p backend/app/middleware
    
    # 依赖注入目录
    mkdir -p backend/app/dependencies
    
    # 任务队列目录
    mkdir -p backend/app/tasks
    
    # 静态文件目录
    mkdir -p backend/static/{css,js,images}
    
    # 模板目录
    mkdir -p backend/templates/{contracts,documents}
    
    # 日志目录
    mkdir -p backend/logs
    
    # 上传目录
    mkdir -p backend/uploads/{contracts,documents,temp}
    
    echo -e "${GREEN}✅ 后端目录结构创建完成${NC}"
}

# 创建前端目录结构
create_frontend_structure() {
    echo "⚛️ 创建前端目录结构..."
    
    # Web前端目录
    mkdir -p frontend/web/{src/{components,pages,hooks,utils,types,services,store,assets},public}
    
    # 组件目录细分
    mkdir -p frontend/web/src/components/{common,layout,forms,charts,modals}
    
    # 页面目录细分
    mkdir -p frontend/web/src/pages/{auth,dashboard,qa,cases,contracts,documents,disputes,profile}
    
    # 服务目录细分
    mkdir -p frontend/web/src/services/{api,auth,storage}
    
    # 资源目录
    mkdir -p frontend/web/src/assets/{images,icons,fonts,styles}
    
    # 移动端目录
    mkdir -p frontend/mobile/{src/{components,screens,navigation,services,utils,types},assets}
    
    # 管理后台目录
    mkdir -p frontend/admin/{src/{components,pages,services,utils},public}
    
    echo -e "${GREEN}✅ 前端目录结构创建完成${NC}"
}

# 创建数据目录结构
create_data_structure() {
    echo "📊 创建数据目录结构..."
    
    # 数据文件目录
    mkdir -p data/{legal_cases,regulations,templates/{contracts,documents},knowledge_graph}
    
    # 训练数据目录
    mkdir -p data/training/{qa_pairs,case_analysis,contract_risk}
    
    # 导入导出目录
    mkdir -p data/{import,export,backup}
    
    echo -e "${GREEN}✅ 数据目录结构创建完成${NC}"
}

# 创建部署目录结构
create_deployment_structure() {
    echo "🚀 创建部署目录结构..."
    
    # Docker配置目录
    mkdir -p deployment/docker/{backend,frontend,nginx}
    
    # Kubernetes配置目录
    mkdir -p deployment/kubernetes/{base,overlays/{development,staging,production}}
    
    # Helm图表目录
    mkdir -p deployment/helm/ai-legal-assistant/{templates,charts}
    
    # 脚本目录
    mkdir -p deployment/scripts/{backup,migration,monitoring}
    
    echo -e "${GREEN}✅ 部署目录结构创建完成${NC}"
}

# 创建测试目录结构
create_test_structure() {
    echo "🧪 创建测试目录结构..."
    
    # 单元测试目录
    mkdir -p tests/unit/{backend,frontend}
    
    # 集成测试目录
    mkdir -p tests/integration/{api,database,services}
    
    # 端到端测试目录
    mkdir -p tests/e2e/{scenarios,fixtures,reports}
    
    # 性能测试目录
    mkdir -p tests/performance/{load,stress,benchmark}
    
    echo -e "${GREEN}✅ 测试目录结构创建完成${NC}"
}

# 创建文档目录结构
create_docs_structure() {
    echo "📚 创建文档目录结构..."
    
    # API文档目录
    mkdir -p docs/{api,architecture,deployment,user_guide}
    
    # 开发文档目录
    mkdir -p docs/development/{setup,coding_standards,contributing}
    
    # 设计文档目录
    mkdir -p docs/design/{ui_ux,database,system}
    
    echo -e "${GREEN}✅ 文档目录结构创建完成${NC}"
}

# 创建基础文件
create_base_files() {
    echo "📄 创建基础文件..."
    
    # 后端__init__.py文件
    find backend -type d -name "*.py" -prune -o -type d -exec touch {}/__init__.py \;
    
    # 前端基础文件
    touch frontend/web/src/index.tsx
    touch frontend/web/src/App.tsx
    touch frontend/web/public/index.html
    
    # 配置文件
    touch .env.example
    touch .env.development
    touch .env.production
    
    # Git相关文件
    touch .gitattributes
    
    # 文档文件
    touch docs/CHANGELOG.md
    touch docs/CONTRIBUTING.md
    touch docs/DEPLOYMENT.md
    
    echo -e "${GREEN}✅ 基础文件创建完成${NC}"
}

# 设置文件权限
set_permissions() {
    echo "🔐 设置文件权限..."
    
    # 脚本文件可执行权限
    find scripts -name "*.sh" -exec chmod +x {} \;
    find deployment/scripts -name "*.sh" -exec chmod +x {} \;
    
    # 日志目录权限
    chmod 755 backend/logs
    chmod 755 logs
    
    # 上传目录权限
    chmod 755 backend/uploads
    
    echo -e "${GREEN}✅ 文件权限设置完成${NC}"
}

# 显示项目结构
show_project_structure() {
    echo ""
    echo "📋 项目结构概览："
    echo "ai-legal-assistant/"
    echo "├── backend/                 # 后端服务"
    echo "│   ├── app/                # 应用代码"
    echo "│   │   ├── api/           # API路由"
    echo "│   │   ├── core/          # 核心模块"
    echo "│   │   ├── db/            # 数据库"
    echo "│   │   ├── models/        # 数据模型"
    echo "│   │   ├── schemas/       # 数据模式"
    echo "│   │   ├── services/      # 业务服务"
    echo "│   │   └── utils/         # 工具函数"
    echo "│   ├── logs/              # 日志文件"
    echo "│   ├── static/            # 静态文件"
    echo "│   ├── templates/         # 模板文件"
    echo "│   └── uploads/           # 上传文件"
    echo "├── frontend/               # 前端应用"
    echo "│   ├── web/               # Web端"
    echo "│   ├── mobile/            # 移动端"
    echo "│   └── admin/             # 管理后台"
    echo "├── data/                   # 数据文件"
    echo "├── deployment/             # 部署配置"
    echo "├── tests/                  # 测试文件"
    echo "├── docs/                   # 文档"
    echo "└── scripts/                # 脚本工具"
}

# 主函数
main() {
    echo "🎯 AI法律助手项目结构创建"
    echo "=========================="
    
    create_backend_structure
    create_frontend_structure
    create_data_structure
    create_deployment_structure
    create_test_structure
    create_docs_structure
    create_base_files
    set_permissions
    show_project_structure
    
    echo ""
    echo -e "${GREEN}🎉 项目结构创建完成！${NC}"
    echo ""
    echo "📝 下一步："
    echo "  1. 查看项目结构是否符合需求"
    echo "  2. 根据需要调整目录结构"
    echo "  3. 开始编写具体的代码文件"
}

# 错误处理
trap 'echo -e "${RED}❌ 脚本执行失败${NC}"; exit 1' ERR

# 执行主函数
main "$@"
